// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

const TableNameServiceAssociation = "service_association"

// ServiceAssociation mapped from table <service_association>
type ServiceAssociation struct {
	ID                int64                           `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	SourceServiceID   int64                           `gorm:"column:source_service_id;type:bigint;not null;uniqueIndex:idx_service_association_service_type,priority:1;uniqueIndex:idx_service_association_care_type,priority:1;uniqueIndex:idx_service_association_service,priority:1;comment:The ID of the service/addon that the rule is configured FOR." json:"source_service_id"` // The ID of the service/addon that the rule is configured FOR.
	TargetServiceID   int64                           `gorm:"column:target_service_id;type:bigint;not null;uniqueIndex:idx_service_association_service,priority:2;comment:Rule Type 1: The ID of a specific service/addon that is being linked TO." json:"target_service_id"`                                                                                                          // Rule Type 1: The ID of a specific service/addon that is being linked TO.
	TargetCareTypeID  int64                           `gorm:"column:target_care_type_id;type:bigint;not null;uniqueIndex:idx_service_association_care_type,priority:2;comment:Rule Type 2: The ID of a care type. Links to all services within this category." json:"target_care_type_id"`                                                                                             // Rule Type 2: The ID of a care type. Links to all services within this category.
	TargetServiceType offeringpb.Service_Type         `gorm:"column:target_service_type;type:character varying(50);not null;uniqueIndex:idx_service_association_service_type,priority:2;comment:Rule Type 3: The type of service (0=Service, 1=Addon). Links to all offerings of this type.;serializer:proto_enum" json:"target_service_type"`                                         // Rule Type 3: The type of service (0=Service, 1=Addon). Links to all offerings of this type.
	CreateTime        *time.Time                      `gorm:"column:create_time;type:timestamp with time zone;not null;default:now()" json:"create_time"`
	UpdateTime        *time.Time                      `gorm:"column:update_time;type:timestamp with time zone;not null;default:now()" json:"update_time"`
	DeleteTime        *time.Time                      `gorm:"column:delete_time;type:timestamp with time zone" json:"delete_time"`
	OrganizationType  organizationpb.OrganizationType `gorm:"column:organization_type;type:character varying(50);not null;index:idx_service_association_organization,priority:1;serializer:proto_enum" json:"organization_type"`
	OrganizationID    int64                           `gorm:"column:organization_id;type:bigint;not null;index:idx_service_association_organization,priority:2" json:"organization_id"`
}

// TableName ServiceAssociation's table name
func (*ServiceAssociation) TableName() string {
	return TableNameServiceAssociation
}
