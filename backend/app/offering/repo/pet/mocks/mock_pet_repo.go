// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego/backend/app/offering/repo/pet (interfaces: Repository)
//
// Generated by this command:
//
//	mockgen -package=mock -destination=mocks/mock_pet_repo.go . Repository
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
	isgomock struct{}
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// BatchGetPetInfo mocks base method.
func (m *MockRepository) BatchGetPetInfo(ctx context.Context, petIDs []int64) ([]*businesscustomerpb.BusinessCustomerPetInfoModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPetInfo", ctx, petIDs)
	ret0, _ := ret[0].([]*businesscustomerpb.BusinessCustomerPetInfoModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetPetInfo indicates an expected call of BatchGetPetInfo.
func (mr *MockRepositoryMockRecorder) BatchGetPetInfo(ctx, petIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPetInfo", reflect.TypeOf((*MockRepository)(nil).BatchGetPetInfo), ctx, petIDs)
}
