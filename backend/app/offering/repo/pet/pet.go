package pet

import (
	"context"

	customerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	svcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

//go:generate mockgen -package=mock -destination=mocks/mock_pet_repo.go . Repository
type Repository interface {
	BatchGetPetInfo(ctx context.Context, petIDs []int64) ([]*customerpb.BusinessCustomerPetInfoModel, error)
	BatchGetPetExtraInfo(
		ctx context.Context, petIDs []int64) ([]*svcpb.BatchGetPetExtraInfoResponse_PetExtraInfo, error)
}

type repository struct {
	petClient svcpb.BusinessCustomerPetServiceClient
}

func NewRepository() Repository {
	return &repository{
		petClient: grpc.NewClient("moego-svc-business-customer", svcpb.NewBusinessCustomerPetServiceClient),
	}
}

func (i *repository) BatchGetPetInfo(
	ctx context.Context, petIDs []int64) ([]*customerpb.BusinessCustomerPetInfoModel, error) {
	response, err := i.petClient.BatchGetPetInfo(ctx, &svcpb.BatchGetPetInfoRequest{
		Ids: petIDs,
	})
	if err != nil {
		log.ErrorContextf(ctx, "BatchGetPetInfo err:%+v, petIDs:%v", err, petIDs)

		return nil, err
	}

	return response.GetPets(), nil
}

func (i *repository) BatchGetPetExtraInfo(
	ctx context.Context, petIDs []int64) ([]*svcpb.BatchGetPetExtraInfoResponse_PetExtraInfo, error) {

	response, err := i.petClient.BatchGetPetExtraInfo(ctx, &svcpb.BatchGetPetExtraInfoRequest{
		Ids: petIDs,
	})
	if err != nil {
		log.ErrorContextf(ctx, "BatchGetPetExtraInfo err:%+v, petIDs:%v", err, petIDs)

		return nil, err
	}

	return response.GetExtraInfos(), nil
}
