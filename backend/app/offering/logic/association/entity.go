package association

import (
	"fmt"

	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

// ServiceAssociationAggregate 表示一个服务的所有关联配置
type ServiceAssociationAggregate struct {
	// 组织类型
	OrganizationType organizationpb.OrganizationType
	// 组织ID
	OrganizationID int64
	// 源服务ID（配置该关联的服务/addon）
	SourceServiceID int64
	// 目标护理类型ID列表（适用于该护理类型下的所有服务）
	TargetCareTypeIDs []int64
	// 目标服务ID列表（适用于特定服务）
	TargetServiceIDs []int64
	// 目标服务类型列表（适用于所有该类型的服务）
	TargetServiceTypes []offeringpb.Service_Type
}

// NewAggregate 创建新的 ServiceAssociationAggregate
func NewAggregate(orgType organizationpb.OrganizationType, orgID, sourceServiceID int64) *ServiceAssociationAggregate {
	return &ServiceAssociationAggregate{
		OrganizationType:   orgType,
		OrganizationID:     orgID,
		SourceServiceID:    sourceServiceID,
		TargetCareTypeIDs:  make([]int64, 0),
		TargetServiceIDs:   make([]int64, 0),
		TargetServiceTypes: make([]offeringpb.Service_Type, 0),
	}
}

// AddCareTypeTarget 添加护理类型目标
func (sa *ServiceAssociationAggregate) AddCareTypeTarget(targetCareTypeID int64) {
	sa.TargetCareTypeIDs = append(sa.TargetCareTypeIDs, targetCareTypeID)
}

// AddServiceIDTarget 添加服务ID目标
func (sa *ServiceAssociationAggregate) AddServiceIDTarget(targetServiceID int64) {
	sa.TargetServiceIDs = append(sa.TargetServiceIDs, targetServiceID)
}

// AddServiceTypeTarget 添加服务类型目标
func (sa *ServiceAssociationAggregate) AddServiceTypeTarget(targetServiceType offeringpb.Service_Type) {
	sa.TargetServiceTypes = append(sa.TargetServiceTypes, targetServiceType)
}

// IsValid 验证关联配置的有效性
func (sa *ServiceAssociationAggregate) IsValid() error {
	if sa.OrganizationType == organizationpb.OrganizationType_ORGANIZATION_TYPE_UNSPECIFIED {
		return fmt.Errorf("organization type cannot be unspecified")
	}
	if sa.OrganizationID == 0 {
		return fmt.Errorf("organization ID cannot be 0")
	}
	if sa.SourceServiceID == 0 {
		return fmt.Errorf("source service ID cannot be 0")
	}

	// 至少需要有一个目标配置
	totalTargets := len(sa.TargetCareTypeIDs) + len(sa.TargetServiceIDs) + len(sa.TargetServiceTypes)
	if totalTargets == 0 {
		return fmt.Errorf("at least one target must be configured")
	}

	return nil
}

// IsAllServices 判断是否适用于所有服务
func (sa *ServiceAssociationAggregate) IsAllServices() bool {
	return len(sa.TargetServiceTypes) == 1 && sa.TargetServiceTypes[0] == offeringpb.Service_SERVICE
}

// IsEmpty 判断是否为空
func (sa *ServiceAssociationAggregate) IsEmpty() bool {
	return len(sa.TargetCareTypeIDs) == 0 && len(sa.TargetServiceIDs) == 0 && len(sa.TargetServiceTypes) == 0
}

// GetTargetCount 获取目标总数
func (sa *ServiceAssociationAggregate) GetTargetCount() int {
	return len(sa.TargetCareTypeIDs) + len(sa.TargetServiceIDs) + len(sa.TargetServiceTypes)
}
