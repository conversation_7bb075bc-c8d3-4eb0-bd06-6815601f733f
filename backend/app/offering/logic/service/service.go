package service

import (
	"context"
	"errors"
	"fmt"

	"github.com/samber/lo"

	organization2 "github.com/MoeGolibrary/moego/backend/app/offering/logic/organization"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/additional"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/attribute"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/availability"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/obsetting"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/override"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/transaction"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/lodging"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	service2 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/organization"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

func NewLogic() *Logic {
	return &Logic{
		query:                  query.Use(db.GetDB()),
		repo:                   service2.NewRepository(),
		lodgingRepo:            lodging.NewRepository(),
		obSettingLogic:         obsetting.New(),
		manager:                attribute.NewManager(),
		availabilityManager:    availability.NewManager(),
		businessScopeLogic:     NewBusinessScope(),
		staffProcessor:         attribute.NewStaffProcessor(),
		lodgingProcessor:       attribute.NewLodgingProcessor(),
		petAvailabilityManager: availability.NewManager(),
		additionalLogic:        additional.NewLogic(),
		overrideLogic:          override.NewLogic(),
		organizationLogic:      organization2.NewLogic(organization.NewRepository()),
	}
}

type Logic struct {
	query                  transaction.Manager
	repo                   service2.Repository
	lodgingRepo            lodging.Repository
	manager                *attribute.Manager
	availabilityManager    *availability.Manager
	obSettingLogic         *obsetting.Logic
	businessScopeLogic     *BusinessScope
	staffProcessor         *attribute.StaffProcessor
	lodgingProcessor       *attribute.LodgingProcessor
	petAvailabilityManager *availability.Manager
	additionalLogic        *additional.Logic
	overrideLogic          *override.Logic
	organizationLogic      *organization2.Logic
}

func (l *Logic) checkNameUnique(
	ctx context.Context,
	orgType organizationpb.OrganizationType,
	orgID int64,
	careTypeID int64,
	name string,
) error {
	_, total, err := l.repo.List(ctx, &service2.ListServiceFilter{
		OrganizationType: orgType,
		OrganizationID:   orgID,
		Types:            []offeringpb.Service_Type{offeringpb.Service_SERVICE},
		CareTypeIDs:      []int64{careTypeID},
		Keyword:          lo.ToPtr(name),
	}, nil)
	if err != nil {
		return err
	}
	if total > 0 {
		return errors.New("service name already exists")
	}

	return nil
}

// CreateService creates a new service aggregate.
func (l *Logic) CreateService(ctx context.Context, createDef *offeringpb.ServiceCreateDef) (int64, error) {
	if err := l.checkNameUnique(
		ctx, createDef.GetOrganizationType(),
		createDef.GetOrganizationId(),
		createDef.GetCareTypeId(),
		createDef.GetName()); err != nil {
		return 0, err
	}

	m := CreateDefToModel(createDef)
	err := l.query.Transaction(func(tx *query.Query) error {
		// 1. 保存 Service
		if err := l.repo.WithQuery(tx).Create(ctx, m); err != nil {
			return err
		}
		serviceID := m.ID

		// 2. 更新 sort 为 ID
		m.Sort = serviceID
		if err := l.repo.WithQuery(tx).Update(ctx, m); err != nil {
			return err
		}

		// 3. 保存 Attributes
		if err := l.manager.Save(ctx, tx, serviceID, createDef.GetAttributes()); err != nil {
			return err
		}

		orgType, orgID := createDef.GetOrganizationType(), createDef.GetOrganizationId()
		// 4. 保存 AvailableBusiness
		if err := l.businessScopeLogic.CreateBusinessScope(ctx, tx, AvailableBusinessScope{
			OrganizationType:  orgType,
			OrganizationID:    orgID,
			ServiceID:         serviceID,
			AvailableBusiness: createDef.GetAvailableBusiness(),
		}); err != nil {
			return err
		}

		// 5. 保存 AdditionalService
		if createDef.GetAdditionalService() != nil {
			if err := l.additionalLogic.Create(
				ctx, tx, orgType, orgID, serviceID, createDef.GetAdditionalService()); err != nil {
				return err
			}
		}

		// 6. 保存 Pet availability
		if err := l.availabilityManager.Save(ctx, tx, &availability.PetAvailability{
			OrganizationType: orgType,
			OrganizationID:   orgID,
			ServiceID:        serviceID,
			PetTypeBreed:     createDef.GetAvailableTypeBreed(),
			PetSize:          createDef.GetAvailablePetSize(),
			CoatType:         createDef.GetAvailableCoatType(),
			PetCode:          createDef.GetAvailablePetCode(),
			PetWeight:        createDef.GetAvailablePetWeight(),
		}); err != nil {
			return err
		}

		// 7. 保存 Business and staff override
		if err := l.overrideLogic.Save(ctx, tx, serviceID, createDef.GetBusinessStaffOverrides()); err != nil {
			return err
		}

		// 8. 保存 Online booking setting
		if err := l.obSettingLogic.InitByServiceID(ctx, tx, serviceID); err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return 0, err
	}

	return m.ID, nil
}

// UpdateService updates a service aggregate.
func (l *Logic) UpdateService(ctx context.Context, updateDef *offeringpb.ServiceUpdateDef) error {
	existing, err := l.repo.Get(ctx, updateDef.GetId())
	if err != nil {
		return err
	}
	if existing == nil {
		return errors.New("service not found")
	}

	// 如果名称有变化，检查名称唯一性
	if updateDef.GetName() != existing.Name {
		if err := l.checkNameUnique(ctx,
			existing.OrganizationType,
			existing.OrganizationID,
			existing.CareTypeID,
			updateDef.GetName()); err != nil {
			return err
		}
	}

	err = l.query.Transaction(func(tx *query.Query) error {
		// 1. 保存 Service
		m := UpdateDefToModel(updateDef)
		if err := l.repo.WithQuery(tx).Update(ctx, m); err != nil {
			return err
		}
		serviceID := m.ID

		// 2. 保存新的 Attributes
		if err := l.manager.Save(ctx, tx, serviceID, updateDef.GetAttributes()); err != nil {
			return err
		}

		orgType, orgID := existing.OrganizationType, existing.OrganizationID
		// 3. 保存 AvailableBusiness
		scope := AvailableBusinessScope{
			OrganizationType:  orgType,
			OrganizationID:    orgID,
			ServiceID:         serviceID,
			AvailableBusiness: updateDef.GetAvailableBusiness(),
		}
		if err := l.businessScopeLogic.UpdateBusinessScope(ctx, tx, scope); err != nil {
			return err
		}

		// 4. 保存 AdditionalService
		if updateDef.GetAdditionalService() != nil {
			if err := l.additionalLogic.Upsert(
				ctx, tx, orgType, orgID, serviceID, updateDef.GetAdditionalService()); err != nil {
				return err
			}
		}

		// 5. 保存 Pet availability
		petAvailability := &availability.PetAvailability{
			OrganizationType: orgType,
			OrganizationID:   orgID,
			ServiceID:        serviceID,
			PetTypeBreed:     updateDef.GetAvailableTypeBreed(),
			PetSize:          updateDef.GetAvailablePetSize(),
			CoatType:         updateDef.GetAvailableCoatType(),
			PetCode:          updateDef.GetAvailablePetCode(),
			PetWeight:        updateDef.GetAvailablePetWeight(),
		}
		if err := l.availabilityManager.Update(ctx, tx, petAvailability); err != nil {
			return err
		}

		// 6. 保存 Business and staff override
		if err := l.overrideLogic.Save(
			ctx, tx, serviceID, updateDef.GetBusinessStaffOverrides()); err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}

	return nil
}

// GetService gets a service by ID.
func (l *Logic) GetService(ctx context.Context, id int64) (*offeringpb.Service, error) {
	// 1. 获取 Service
	m, err := l.repo.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 2. 获取 Attribute values
	attributes, err := l.manager.ListByServiceID(ctx, id)
	if err != nil {
		return nil, err
	}

	// 3. 获取 Business scope
	businessScope, err := l.businessScopeLogic.ListBusinessScopes(ctx, []int64{id})
	if err != nil {
		return nil, err
	}

	// 4. 获取 AdditionalService
	additionalService, err := l.additionalLogic.GetByServiceID(ctx, id)
	if err != nil {
		return nil, err
	}

	// 5. 获取 Pet availability
	petAvailabilities, err := l.availabilityManager.ListByServiceIDs(ctx, []int64{id})
	if err != nil {
		return nil, err
	}
	petAvailability := petAvailabilities[id]

	// 6. 获取 Business and staff override
	overridesMap, err := l.overrideLogic.ListByServiceIDs(ctx, []int64{id})
	if err != nil {
		return nil, err
	}
	overrides := overridesMap[id]

	pb := ModelToProto(m)
	pb.Attributes = attributes
	pb.AvailableBusiness = businessScope[id]
	pb.AdditionalService = additionalService

	// 设置 Pet availability 信息
	if petAvailability != nil {
		pb.AvailableTypeBreed = petAvailability.PetTypeBreed
		pb.AvailablePetSize = petAvailability.PetSize
		pb.AvailableCoatType = petAvailability.CoatType
		pb.AvailablePetCode = petAvailability.PetCode
		pb.AvailablePetWeight = petAvailability.PetWeight
	}

	// 设置 Business and staff overrides
	if overrides != nil {
		pb.BusinessStaffOverrides = overrides
	}

	return pb, nil
}

// DeleteService deletes a service.
func (l *Logic) DeleteService(ctx context.Context, id int64) error {
	err := l.query.Transaction(func(tx *query.Query) error {
		// 1. 删除 Attributes
		if err := l.manager.DeleteByServiceID(ctx, tx, id); err != nil {
			return err
		}

		// 2. 删除 Business scope
		if err := l.businessScopeLogic.DeleteBusinessScope(ctx, tx, id); err != nil {
			return err
		}

		// 3. 删除 AdditionalService
		if err := l.additionalLogic.DeleteByServiceID(ctx, tx, id); err != nil {
			return err
		}

		// 4. 删除 Pet availability
		if err := l.availabilityManager.DeleteByServiceID(ctx, tx, id); err != nil {
			return err
		}

		// 5. 删除 Business and staff override
		if err := l.overrideLogic.DeleteByServiceID(ctx, tx, id); err != nil {
			return err
		}

		// 6. 删除 Service
		return l.repo.WithQuery(tx).Delete(ctx, id)
	})
	if err != nil {
		return err
	}

	return nil
}

// ListServices lists services based on the given request parameters.
func (l *Logic) ListServices(ctx context.Context,
	req *offeringpb.ListServicesRequest) (*offeringpb.ListServicesResponse, error) {
	// 构造过滤条件
	filter := l.buildListServicesFilter(req)

	// 查询服务列表
	services, total, err := l.repo.List(ctx, filter, req.Pagination)
	if err != nil {
		return nil, err
	}

	// 批量加载并丰富关联数据
	serviceProtos, err := l.enrichServicesWithRelatedData(ctx, services)
	if err != nil {
		return nil, err
	}

	// 构造响应
	resp := &offeringpb.ListServicesResponse{
		Services: []*offeringpb.Service{},
		Total:    int32(total),
	}
	// 添加 services
	resp.Services = serviceProtos

	// 添加分页信息
	if req.Pagination != nil {
		resp.Pagination = req.Pagination
	}

	return resp, nil
}

// buildListServicesFilter 构造服务列表查询的过滤条件
func (l *Logic) buildListServicesFilter(req *offeringpb.ListServicesRequest) *service2.ListServiceFilter {
	var filter = &service2.ListServiceFilter{
		OrganizationType: req.OrganizationType,
		OrganizationID:   req.OrganizationId,
		Types:            []offeringpb.Service_Type{offeringpb.Service_SERVICE},
	}
	if req.Filter != nil {
		filter.CareTypeIDs = req.Filter.CareTypeIds
		filter.CategoriesIDs = req.Filter.CategoryIds
		filter.Statuses = req.Filter.Statuses
	}

	return filter
}

// enrichServicesWithRelatedData 将模型转换为proto并批量加载丰富关联数据
func (l *Logic) enrichServicesWithRelatedData(
	ctx context.Context, services []*model.Service) ([]*offeringpb.Service, error) {
	if len(services) == 0 {
		return []*offeringpb.Service{}, nil
	}

	// 转换为 proto 对象
	serviceProtos := lo.Map(services, func(service *model.Service, _ int) *offeringpb.Service {
		return ModelToProto(service)
	})

	// 提取服务ID列表
	serviceIDs := lo.Map(serviceProtos, func(service *offeringpb.Service, _ int) int64 {
		return service.Id
	})

	// 1. 批量获取 Attributes
	attributesMap, err := l.manager.ListByServiceIDs(ctx, serviceIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to load attributes: %w", err)
	}

	// 2. 批量获取 Business scopes
	businessScopes, err := l.businessScopeLogic.ListBusinessScopes(ctx, serviceIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to load business scopes: %w", err)
	}

	// 3. 批量获取 AdditionalServices
	additionalServices, err := l.additionalLogic.ListByServiceIDs(ctx, serviceIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to load additional services: %w", err)
	}

	// 4. 批量获取 Pet availabilities
	petAvailabilities, err := l.availabilityManager.ListByServiceIDs(ctx, serviceIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to load pet availabilities: %w", err)
	}

	// 5. 批量获取 Business and staff overrides
	businessStaffOverrides, err := l.overrideLogic.ListByServiceIDs(ctx, serviceIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to load business staff overrides: %w", err)
	}

	// 6. 批量获取 pet override
	petOverrides, _, err := l.overrideLogic.ListPetOverride(ctx, &service2.ListPetOverrideFilter{
		ServiceIDs: serviceIDs,
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to load pet overrides: %w", err)
	}
	petOverridesMap := lo.GroupBy(petOverrides, func(o *offeringpb.PetOverride) int64 {
		return o.ServiceId
	})

	// 7. 直接丰富服务对象
	for _, service := range serviceProtos {
		// 设置 Attributes
		if attributes, exists := attributesMap[service.Id]; exists {
			service.Attributes = attributes
		} else {
			service.Attributes = &offeringpb.ServiceAttributes{}
		}

		// 设置 AvailableBusiness
		if businessScope, exists := businessScopes[service.Id]; exists {
			service.AvailableBusiness = businessScope
		}

		// 设置 AdditionalService
		if additionalService, exists := additionalServices[service.Id]; exists {
			service.AdditionalService = additionalService
		}

		// 设置 Pet availability
		if petAvailability, exists := petAvailabilities[service.Id]; exists {
			service.AvailableTypeBreed = petAvailability.PetTypeBreed
			service.AvailablePetSize = petAvailability.PetSize
			service.AvailableCoatType = petAvailability.CoatType
			service.AvailablePetCode = petAvailability.PetCode
			service.AvailablePetWeight = petAvailability.PetWeight
		}

		// 设置 Business and staff overrides
		if overrides, exists := businessStaffOverrides[service.Id]; exists {
			service.BusinessStaffOverrides = overrides
		}

		// 设置 Pet override
		if overrides, exists := petOverridesMap[service.Id]; exists {
			service.PetOverrides = overrides
		}
	}

	return serviceProtos, nil
}

// ListAvailableServices 根据上下文信息查询可用服务列表
func (l *Logic) ListAvailableServices(ctx context.Context,
	req *offeringpb.ListAvailableServicesRequest) (*offeringpb.ListAvailableServicesResponse, error) {
	// 1. 获取租户信息
	orgType, orgID, err := l.organizationLogic.GetOrganizationInfo(
		ctx, req.GetOrganizationType(), req.GetOrganizationId())
	if err != nil {
		return nil, err
	}
	// 2. 构造过滤条件
	filter := RequestToFilter(req)
	filter.OrganizationType = orgType
	filter.OrganizationID = orgID

	// 3. 检查是否应该应用可用性检查
	if l.shouldApplyAvailabilityChecks(req) {
		serviceIDs, err := l.getAvailableServiceIDs(ctx, req, orgType, orgID)
		if err != nil {
			return nil, err
		}
		if len(serviceIDs) == 0 {
			return l.buildEmptyAvailableServicesResponse(req), nil
		}
		filter.IDs = serviceIDs
	}

	// 4. 根据 filter 查询服务，包含分页
	services, total, err := l.repo.List(ctx, filter, req.Pagination)
	if err != nil {
		return nil, err
	}

	// 5. 转换为 proto 对象，并丰富关联数据
	serviceProtos, err := l.buildAvailableServicesResponse(ctx, services)
	if err != nil {
		return nil, err
	}

	// 构造响应
	resp := &offeringpb.ListAvailableServicesResponse{
		Services: serviceProtos,
		Total:    int32(total),
	}

	// 添加分页信息
	if req.Pagination != nil {
		resp.Pagination = req.Pagination
	}

	return resp, nil
}

// shouldApplyAvailabilityChecks 检查是否应该应用可用性检查
func (l *Logic) shouldApplyAvailabilityChecks(req *offeringpb.ListAvailableServicesRequest) bool {
	return req.GetOrganizationType() == organizationpb.OrganizationType_BUSINESS ||
		(req.GetContext() != nil && (req.GetContext().StaffId != nil ||
			req.GetContext().LodgingUnitId != nil ||
			len(req.GetContext().GetPetIds()) > 0 ||
			len(req.GetContext().GetServiceIds()) > 0))
}

// getAvailableServiceIDs 获取所有可用性检查的服务 ID 列表
func (l *Logic) getAvailableServiceIDs(
	ctx context.Context, req *offeringpb.ListAvailableServicesRequest,
	orgType organizationpb.OrganizationType, orgID int64) ([]int64, error) {

	// 用二维数组保存所有查询结果
	var allServiceIDLists [][]int64

	// 1. Business scope 检查
	if req.GetOrganizationType() == organizationpb.OrganizationType_BUSINESS {
		serviceIDs, err := l.businessScopeLogic.ListAvailableServiceIDs(
			ctx, orgType, orgID, req.GetOrganizationId())
		if err != nil {
			return nil, fmt.Errorf("failed to get business available service IDs: %w", err)
		}
		allServiceIDLists = append(allServiceIDLists, serviceIDs)
	}

	// 2. Staff scope 检查
	if req.GetContext().StaffId != nil {
		serviceIDs, err := l.staffProcessor.ListAvailableServiceIDs(
			ctx, orgType, orgID, req.GetContext().GetStaffId())
		if err != nil {
			return nil, fmt.Errorf("failed to get staff available service IDs: %w", err)
		}
		allServiceIDLists = append(allServiceIDLists, serviceIDs)
	}

	// 3. Lodging scope 检查
	if req.GetContext().LodgingUnitId != nil {
		lodgingUnit, err := l.lodgingRepo.GetLodgingUnit(ctx, req.GetContext().GetLodgingUnitId())
		if err != nil {
			return nil, fmt.Errorf("failed to get lodging unit: %w", err)
		}

		serviceIDs, err := l.lodgingProcessor.ListAvailableServiceIDs(
			ctx, orgType, orgID, lodgingUnit.LodgingTypeID)
		if err != nil {
			return nil, fmt.Errorf("failed to get lodging available service IDs: %w", err)
		}
		allServiceIDLists = append(allServiceIDLists, serviceIDs)
	}

	// 4. Pet availability 检查
	if len(req.GetContext().GetPetIds()) > 0 {
		serviceIDs, err := l.petAvailabilityManager.ListAvailableServiceIDs(
			ctx, orgType, orgID, req.GetContext().GetPetIds())
		if err != nil {
			return nil, fmt.Errorf("failed to get pet available service IDs: %w", err)
		}
		allServiceIDLists = append(allServiceIDLists, serviceIDs)
	}

	// 5. Additional service 检查
	if len(req.GetContext().GetServiceIds()) > 0 {
		serviceIDs, err := l.additionalLogic.ListAdditionalServiceIDs(
			ctx, orgType, orgID, req.GetContext().GetServiceIds())
		if err != nil {
			return nil, fmt.Errorf("failed to get additional service available service IDs: %w", err)
		}
		allServiceIDLists = append(allServiceIDLists, serviceIDs)
	}

	// 如果没有启用的检查，返回空列表
	if len(allServiceIDLists) == 0 {
		return []int64{}, nil
	}

	// 遍历二维数组取交集
	result := allServiceIDLists[0]
	for i := 1; i < len(allServiceIDLists); i++ {
		result = lo.Intersect(result, allServiceIDLists[i])
	}

	return result, nil
}

// buildEmptyAvailableServicesResponse 构造空的可用服务响应
func (l *Logic) buildEmptyAvailableServicesResponse(
	req *offeringpb.ListAvailableServicesRequest) *offeringpb.ListAvailableServicesResponse {
	resp := &offeringpb.ListAvailableServicesResponse{
		Services: []*offeringpb.AvailableService{},
		Total:    0,
	}

	// 添加分页信息
	if req.Pagination != nil {
		resp.Pagination = req.Pagination
	}

	return resp
}

// buildAvailableService 构建可用服务
func (l *Logic) buildAvailableServicesResponse(
	ctx context.Context, services []*model.Service) ([]*offeringpb.AvailableService, error) {
	// 1. 提取服务ID列表
	serviceIDs := lo.Map(services, func(service *model.Service, _ int) int64 {
		return service.ID
	})

	// 2. 批量获取 Attributes
	attributesMap, err := l.manager.ListByServiceIDs(ctx, serviceIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to load attributes: %w", err)
	}

	// 3. 批量获取 Business and staff overrides
	overridesMap, err := l.overrideLogic.ListByServiceIDs(ctx, serviceIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to load overrides: %w", err)
	}

	// 4. 批量获取 Pet overrides
	petOverrides, _, err := l.overrideLogic.ListPetOverride(ctx, &service2.ListPetOverrideFilter{
		ServiceIDs: serviceIDs,
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to load pet overrides: %w", err)
	}
	petOverridesMap := lo.GroupBy(petOverrides, func(petOverride *offeringpb.PetOverride) int64 {
		return petOverride.ServiceId
	})

	// 4. 转换为 proto 对象
	pbs := lo.Map(services, func(service *model.Service, _ int) *offeringpb.AvailableService {
		pb := ModelToAvailableProto(service)
		pb.Attributes = ToAttributes(attributesMap[service.ID])
		pb.BusinessStaffOverrides = overridesMap[service.ID]
		pb.PetOverrides = petOverridesMap[service.ID]

		return pb
	})

	return pbs, nil
}

// BatchUpdateServices 批量更新服务信息
func (l *Logic) BatchUpdateServices(ctx context.Context,
	req *offeringpb.BatchUpdateServicesRequest) (*offeringpb.BatchUpdateServicesResponse, error) {
	err := l.query.Transaction(func(tx *query.Query) error {
		// 1. 获取需要更新的服务列表
		serviceIDs := lo.Map(req.UpdateServices, func(s *offeringpb.ServiceUpdateDef, _ int) int64 { return s.Id })
		filter := &service2.ListServiceFilter{
			OrganizationType: req.OrganizationType,
			OrganizationID:   req.OrganizationId,
			IDs:              serviceIDs,
		}
		services, _, err := l.repo.List(ctx, filter, nil)
		if err != nil {
			return err
		}

		serviceMap := lo.KeyBy(services, func(s *model.Service) int64 { return s.ID })

		// 2. 批量更新服务
		for _, updateDef := range req.UpdateServices {
			// 3. 检查服务是否存在
			_, exists := serviceMap[updateDef.Id]
			if !exists {
				return errors.New("service not found")
			}

			// 更新服务
			update := l.buildUpdateService(updateDef)
			if err := l.repo.WithQuery(tx).Update(ctx, update); err != nil {
				return err
			}

			// 更新关联数据
			if err := l.updateServiceRelatedData(ctx, tx, updateDef, req); err != nil {
				return err
			}
		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	// 4. 构造响应
	return &offeringpb.BatchUpdateServicesResponse{}, nil
}

// buildUpdateService 构造更新服务
func (l *Logic) buildUpdateService(def *offeringpb.ServiceUpdateDef) *model.Service {
	update := &model.Service{
		ID: def.Id,
	}
	if def.Name != nil {
		update.Name = *def.Name
	}
	if def.CategoryId != nil {
		update.CategoryID = *def.CategoryId
	}
	if def.Description != nil {
		update.Description = def.Description
	}
	if def.ColorCode != nil {
		update.ColorCode = *def.ColorCode
	}
	if def.Sort != nil {
		update.Sort = *def.Sort
	}
	if len(def.Images) > 0 {
		update.Images = def.Images
	}
	if def.Status != nil {
		update.Status = *def.Status
	}

	return update
}

// updateServiceRelatedData 更新服务关联数据
func (l *Logic) updateServiceRelatedData(
	ctx context.Context, tx *query.Query,
	updateDef *offeringpb.ServiceUpdateDef,
	req *offeringpb.BatchUpdateServicesRequest) error {
	// 更新 AvailableBusiness
	if updateDef.AvailableBusiness != nil {
		err := l.businessScopeLogic.UpdateBusinessScope(ctx, tx, AvailableBusinessScope{
			OrganizationType:  req.GetOrganizationType(),
			OrganizationID:    req.GetOrganizationId(),
			ServiceID:         updateDef.Id,
			AvailableBusiness: updateDef.AvailableBusiness,
		})
		if err != nil {
			return fmt.Errorf("failed to update business scope: %w", err)
		}
	}

	// 更新 ServiceAttributes
	if updateDef.Attributes != nil {
		if err := l.manager.Save(ctx, tx, updateDef.GetId(), updateDef.GetAttributes()); err != nil {
			return fmt.Errorf("failed to update service attributes: %w", err)
		}
	}

	// 更新 Business and staff override
	if updateDef.BusinessStaffOverrides != nil {
		if err := l.overrideLogic.Save(
			ctx, tx, updateDef.GetId(), updateDef.GetBusinessStaffOverrides()); err != nil {
			return fmt.Errorf("failed to save business staff override: %w", err)
		}
	}

	return nil
}

// ListOBServices 获取指定 business 的在线预约服务设置列表
func (l *Logic) ListOBServices(ctx context.Context,
	req *offeringpb.ListOBServicesRequest) (*offeringpb.ListOBServicesResponse, error) {
	// 提取 filter 中的 service_ids
	var serviceIDs []int64
	if req.Filter != nil {
		serviceIDs = req.Filter.ServiceIds
	}

	// 从 obsetting logic 层获取在线预约设置
	obSettings, total, err := l.obSettingLogic.ListByBusinessID(ctx, req.BusinessId, serviceIDs, req.Pagination)
	if err != nil {
		return nil, err
	}

	// 转换为 proto 对象
	protoSettings := make([]*offeringpb.ServiceOBSetting, 0, len(obSettings))
	for _, setting := range obSettings {
		protoSetting := obsetting.EntityToProto(setting)
		protoSettings = append(protoSettings, protoSetting)
	}

	// 构造响应
	resp := &offeringpb.ListOBServicesResponse{
		ServiceObSettings: protoSettings,
		Total:             int32(total),
	}

	// 添加分页信息
	if req.Pagination != nil {
		resp.Pagination = req.Pagination
	}

	return resp, nil
}

// UpdateOBService updates the online booking setting for a service
func (l *Logic) UpdateOBService(ctx context.Context, req *offeringpb.UpdateOBServiceRequest) error {
	return l.obSettingLogic.UpdateOBService(ctx, req)
}

// BatchGetServices 批量获取服务
func (l *Logic) BatchGetServices(
	ctx context.Context, req *offeringpb.BatchGetServicesRequest) (*offeringpb.BatchGetServicesResponse, error) {
	// 1. 获取服务列表
	filter := &service2.ListServiceFilter{
		OrganizationType: req.GetOrganizationType(),
		OrganizationID:   req.GetOrganizationId(),
		IDs:              req.GetIds(),
	}
	services, _, err := l.repo.List(ctx, filter, nil)
	if err != nil {
		return nil, err
	}

	// 2. 批量加载并丰富关联数据
	serviceProtos, err := l.enrichServicesWithRelatedData(ctx, services)
	if err != nil {
		return nil, err
	}

	return &offeringpb.BatchGetServicesResponse{
		Services: serviceProtos,
	}, nil
}

// BatchGetCustomizedServices 批量获取 customized service
func (l *Logic) BatchGetCustomizedServices(ctx context.Context, req *offeringpb.BatchGetCustomizedServicesRequest,
) ([]*offeringpb.BatchGetCustomizedServicesResponse_ServiceWithCustomized, error) {

	// 1. 获取服务列表
	// 获取 serviceIDs 列表
	serviceIDs := lo.Map(req.GetQueryConditions(), func(
		condition *offeringpb.BatchGetCustomizedServicesRequest_CustomizedServiceQueryCondition, _ int) int64 {
		return condition.GetServiceId()
	})

	// 获取服务列表
	filter := &service2.ListServiceFilter{
		OrganizationType: req.GetOrganizationType(),
		OrganizationID:   req.GetOrganizationId(),
		IDs:              serviceIDs,
	}
	services, _, err := l.repo.List(ctx, filter, nil)
	if err != nil {
		return nil, err
	}

	// 批量加载并关联数据
	servicePbs, err := l.enrichServicesWithRelatedData(ctx, services)
	if err != nil {
		return nil, err
	}

	serviceMap := lo.KeyBy(servicePbs, func(s *offeringpb.Service) int64 { return s.Id })

	// 2. 构建 customized service 结果
	results := make(
		[]*offeringpb.BatchGetCustomizedServicesResponse_ServiceWithCustomized,
		0,
		len(req.GetQueryConditions()))
	for _, condition := range req.GetQueryConditions() {
		customizedServiceWithCondition, err := l.buildCustomizedServiceWithCondition(serviceMap, condition)
		if err != nil {
			return nil, err
		}
		results = append(results, customizedServiceWithCondition)
	}

	return results, nil
}

// buildCustomizedServiceWithCondition 根据条件构建 customized service
func (l *Logic) buildCustomizedServiceWithCondition(
	serviceMap map[int64]*offeringpb.Service,
	condition *offeringpb.BatchGetCustomizedServicesRequest_CustomizedServiceQueryCondition,
) (*offeringpb.BatchGetCustomizedServicesResponse_ServiceWithCustomized, error) {
	service := serviceMap[condition.GetServiceId()]
	if service == nil {
		return nil, errors.New("service not found")
	}

	customizedService := &offeringpb.CustomizedService{
		Service:              service,
		Duration:             service.Attributes.Duration,
		MaxDuration:          service.Attributes.MaxDuration,
		Price:                service.Price,
		TaxId:                service.TaxId,
		PriceOverrideType:    offeringpb.OverrideType_OVERRIDE_TYPE_UNSPECIFIED,
		DurationOverrideType: offeringpb.OverrideType_OVERRIDE_TYPE_UNSPECIFIED,
	}

	// 应用 override 配置
	l.applyOverridesToCustomizedService(customizedService, service, condition)

	return &offeringpb.BatchGetCustomizedServicesResponse_ServiceWithCustomized{
		VirtualId:         condition.GetVirtualId(),
		CustomizedService: customizedService,
	}, nil
}

// applyOverridesToCustomizedService 将 override 配置应用到 customized service
func (l *Logic) applyOverridesToCustomizedService(
	customizedService *offeringpb.CustomizedService,
	service *offeringpb.Service,
	condition *offeringpb.BatchGetCustomizedServicesRequest_CustomizedServiceQueryCondition,
) {
	if condition.GetBusinessId() > 0 {
		// 查找 business override
		businessStaffOverride, found := lo.Find(service.BusinessStaffOverrides,
			func(o *offeringpb.BusinessStaffOverride) bool {
				return o.BusinessOverride.BusinessId == condition.GetBusinessId()
			})
		if found {
			// 应用 business override
			l.applyBusinessOverride(customizedService, businessStaffOverride, condition)

			// 应用 staff override（优先级更高）
			if condition.GetStaffId() > 0 {
				l.applyStaffOverride(customizedService, businessStaffOverride, condition)
			}
		}
	}

	// 应用 pet override（优先级最高）
	if condition.GetPetId() > 0 {
		l.applyPetOverride(customizedService, service, condition)
	}
}

// applyBusinessOverride 应用 business override 配置
func (l *Logic) applyBusinessOverride(
	customizedService *offeringpb.CustomizedService,
	businessOverride *offeringpb.BusinessStaffOverride,
	condition *offeringpb.BatchGetCustomizedServicesRequest_CustomizedServiceQueryCondition,
) {
	if condition.GetBusinessId() <= 0 {
		return
	}

	bo := businessOverride.BusinessOverride
	if bo.Duration != nil {
		customizedService.Duration = bo.Duration
		customizedService.DurationOverrideType = offeringpb.OverrideType_BUSINESS
	}
	if bo.MaxDuration != nil {
		customizedService.MaxDuration = bo.MaxDuration
	}
	if bo.Price != nil {
		customizedService.Price = bo.Price
		customizedService.PriceOverrideType = offeringpb.OverrideType_BUSINESS
	}
	if bo.TaxId != nil {
		customizedService.TaxId = *bo.TaxId
	}
}

// applyStaffOverride 应用 staff override 配置
func (l *Logic) applyStaffOverride(
	customizedService *offeringpb.CustomizedService,
	businessOverride *offeringpb.BusinessStaffOverride,
	condition *offeringpb.BatchGetCustomizedServicesRequest_CustomizedServiceQueryCondition,
) {
	staffOverride, found := lo.Find(businessOverride.StaffOverrides, func(o *offeringpb.StaffOverride) bool {
		return o.StaffId == condition.GetStaffId()
	})
	if !found {
		return
	}

	if staffOverride.Price != nil {
		customizedService.Price = staffOverride.Price
		customizedService.PriceOverrideType = offeringpb.OverrideType_STAFF
	}
	if staffOverride.Duration != nil {
		customizedService.Duration = staffOverride.Duration
		customizedService.DurationOverrideType = offeringpb.OverrideType_STAFF
	}
}

// applyPetOverride 应用 pet override 配置
func (l *Logic) applyPetOverride(
	customizedService *offeringpb.CustomizedService,
	service *offeringpb.Service,
	condition *offeringpb.BatchGetCustomizedServicesRequest_CustomizedServiceQueryCondition,
) {
	petOverride, found := lo.Find(service.PetOverrides, func(o *offeringpb.PetOverride) bool {
		return o.PetId == condition.GetPetId()
	})
	if !found {
		return
	}

	if petOverride.Price != nil {
		customizedService.Price = petOverride.Price
		customizedService.PriceOverrideType = offeringpb.OverrideType_PET
	}
	if petOverride.Duration != nil {
		customizedService.Duration = petOverride.Duration
		customizedService.DurationOverrideType = offeringpb.OverrideType_PET
	}
}

// ListPetOverride lists pet overrides
func (l *Logic) ListPetOverride(ctx context.Context,
	req *offeringpb.ListPetOverrideRequest) (*offeringpb.ListPetOverrideResponse, error) {
	filter := l.buildListPetOverrideFilter(req)
	petOverrides, total, err := l.overrideLogic.ListPetOverride(ctx, filter, req.GetPagination())
	if err != nil {
		return nil, err
	}

	return &offeringpb.ListPetOverrideResponse{
		PetOverrides: petOverrides,
		Pagination:   req.GetPagination(),
		Total:        int32(total),
	}, nil
}

func (l *Logic) buildListPetOverrideFilter(
	req *offeringpb.ListPetOverrideRequest) *service2.ListPetOverrideFilter {
	return &service2.ListPetOverrideFilter{
		PetIDs: req.GetPetIds(),
	}
}

// UpsertPetOverride upserts a pet override
func (l *Logic) UpsertPetOverride(ctx context.Context,
	req *offeringpb.UpsertPetOverrideRequest) (*offeringpb.PetOverride, error) {
	return l.overrideLogic.UpsertPetOverride(ctx, req)
}

// UpdatePetOverride updates a pet override
func (l *Logic) UpdatePetOverride(ctx context.Context,
	req *offeringpb.UpdatePetOverrideRequest) (*offeringpb.PetOverride, error) {
	return l.overrideLogic.UpdatePetOverride(ctx, req)
}

// DeletePetOverride deletes a pet override
func (l *Logic) DeletePetOverride(ctx context.Context, id int64) error {
	return l.overrideLogic.DeletePetOverride(ctx, id)
}
