load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "service",
    srcs = [
        "business_scope.go",
        "converter.go",
        "entity.go",
        "service.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/logic/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/logic/organization",
        "//backend/app/offering/logic/service/additional",
        "//backend/app/offering/logic/service/attribute",
        "//backend/app/offering/logic/service/availability",
        "//backend/app/offering/logic/service/obsetting",
        "//backend/app/offering/logic/service/override",
        "//backend/app/offering/logic/transaction",
        "//backend/app/offering/repo/db",
        "//backend/app/offering/repo/db/lodging",
        "//backend/app/offering/repo/db/model",
        "//backend/app/offering/repo/db/query",
        "//backend/app/offering/repo/db/service",
        "//backend/app/offering/repo/organization",
        "//backend/common/utils/money",
        "//backend/proto/offering/v1:offering",
        "//backend/proto/organization/v1:organization",
        "@com_github_samber_lo//:lo",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "service_test",
    srcs = [
        "business_scope_test.go",
        "converter_test.go",
        "service_test.go",
    ],
    embed = [":service"],
    deps = [
        "//backend/app/offering/logic/service/additional",
        "//backend/app/offering/logic/service/attribute",
        "//backend/app/offering/logic/service/availability",
        "//backend/app/offering/logic/service/obsetting",
        "//backend/app/offering/logic/service/override",
        "//backend/app/offering/logic/transaction/mocks",
        "//backend/app/offering/repo/db/additional/mocks",
        "//backend/app/offering/repo/db/model",
        "//backend/app/offering/repo/db/query",
        "//backend/app/offering/repo/db/service/mocks",
        "//backend/app/offering/repo/db/serviceattribute/mocks",
        "//backend/app/offering/repo/db/serviceobsetting/mocks",
        "//backend/app/offering/repo/organization/mocks",
        "//backend/app/offering/repo/pet/mocks",
        "//backend/proto/offering/v1:offering",
        "//backend/proto/organization/v1:organization",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/organization/v1:organization",
        "@com_github_samber_lo//:lo",
        "@com_github_shopspring_decimal//:decimal",
        "@com_github_stretchr_testify//assert",
        "@org_golang_google_genproto//googleapis/type/money",
        "@org_uber_go_mock//gomock",
    ],
)
