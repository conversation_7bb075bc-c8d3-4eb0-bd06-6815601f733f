package inner

import (
	"context"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/category"

	organization2 "github.com/MoeGolibrary/moego/backend/app/offering/logic/organization"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/transaction"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/lodging"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	service2 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/organization"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/fulfillment"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/serviceinstance"
	fulfillmentpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	offeringinnerpb "github.com/MoeGolibrary/moego/backend/proto/offering/inner"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"

	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/additional"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/attribute"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/availability"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/obsetting"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/override"
)
