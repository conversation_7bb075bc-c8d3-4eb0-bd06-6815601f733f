package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"sync"
	"testing"
	"time"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/configloader"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/global"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/logic/jiraslareminder"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/logic/watcher"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/pkg"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/entity"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/jira"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/slack"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/config/nacos"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
)

var setupTestSvc sync.Once

func setUp() {
	setupTestSvc.Do(func() {
		currentDir, _ := os.Getwd()
		fmt.Printf("currentDir:%v\n", currentDir)
		currentDir = currentDir[:len(currentDir)-len("service")]
		rpc.ServerConfigPath = currentDir + "config/local/config.yaml"
		_ = rpc.NewServer()
	})
}

func TestSendSLAReminder_Manual(t *testing.T) {
	t.Skip("manual test")
	setUp()
	_ = configloader.InitNacosConfigAndGet(context.TODO())
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	jiraClient, err := jira.NewIssueRepository(cfg.CsPageWatcher.JiraEmail, cfg.CsPageWatcher.JiraToken)
	if err != nil {
		t.Fatalf("failed to create jira client: %v", err)
	}
	slackClient := slack.NewClient(cfg.CsPageWatcher.SlackToken)
	jiraReminder := NewScheduledJiraReminder(
		jiraslareminder.NewSLAReminder(),
		slackClient,
		jiraClient,
		cfg.CsPageWatcher.SLAReminderChannelID,
		&configloader.GlobalNacosCsPageConfig)

	p := &PageWatcherIns{
		cfg:           cfg,
		nacosCsConfig: &configloader.GlobalNacosCsPageConfig,
		jiraClient:    jiraClient,
		slackClient:   slackClient,
		csPageRepo:    entity.NewCsPageReaderWriter(),
		locker:        pkg.NewLocalLocker(),
		jiraReminder:  jiraReminder,
	}

	ret, err := p.runJiraSLARemind(context.Background(), global.AdminTaskIssueType,
		"Overdue Open Tasks: AdminTasks", false, false, nil)
	if err != nil {
		t.Errorf("failed to run sla reminder: %v", err)
		return
	}
	t.Logf("ret: %s", ret)
	time.Sleep(60 * time.Second)
}

func TestSendAdminTaskNotify_Manual(t *testing.T) {
	t.Skip("manual test")
	setUp()
	_ = configloader.InitNacosConfigAndGet(context.TODO())
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	jiraClient, err := jira.NewIssueRepository(cfg.CsPageWatcher.JiraEmail, cfg.CsPageWatcher.JiraToken)
	if err != nil {
		t.Fatalf("failed to create jira client: %v", err)
	}
	slackClient := slack.NewClient(cfg.CsPageWatcher.SlackToken)

	p := &PageWatcherIns{
		cfg:           cfg,
		nacosCsConfig: &configloader.GlobalNacosCsPageConfig,
		jiraClient:    jiraClient,
		slackClient:   slackClient,
		csPageRepo:    entity.NewCsPageReaderWriter(),
		locker:        pkg.NewLocalLocker(),
		jiraReminder:  nil,
	}
	p.sendAdminTaskNotification(&jira.Issue{
		Key:           "CS-31838",
		IssuePriority: global.PriorityP0,
		Summary:       "test",
	}, "<EMAIL>")
}

func TestRealJiraEvaluate(t *testing.T) {
	t.Skip("manual test")
	setUp()
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	t.Logf("jira token: %v", cfg.CsPageWatcher.JiraToken)
	r, err := jira.NewIssueRepository(cfg.CsPageWatcher.JiraEmail, cfg.CsPageWatcher.JiraToken)
	assert.Nil(t, err)
	issue, err := r.GetIssueDetails("CS-31838")
	assert.Nil(t, err)
	t.Logf("issue: %s", lo.Must(json.Marshal(issue)))

	evaluator := watcher.NewJiraIncidentEvaluator(&configloader.GlobalNacosCsPageConfig)
	retIncident, err := evaluator.EvaluateBug(issue)
	t.Logf("retIncident: %s", lo.Must(json.Marshal(retIncident)))
}

func TestPageWatcherIns_retryOperation(t *testing.T) {
	tests := []struct {
		name          string
		op            func() error
		errMsgFormat  string
		args          []interface{}
		expectedErr   error
		expectedCalls int
	}{
		{
			name: "success on first try",
			op: func() error {
				return nil
			},
			errMsgFormat:  "operation failed: %v",
			args:          []interface{}{"test"},
			expectedErr:   nil,
			expectedCalls: 1,
		},
		{
			name: "success after retries",
			op: func() func() error {
				callCount := 0
				return func() error {
					callCount++
					if callCount < 3 {
						return errors.New("temporary error")
					}
					return nil
				}
			}(),
			errMsgFormat:  "operation failed: %v",
			args:          []interface{}{"test"},
			expectedErr:   nil,
			expectedCalls: 3,
		},
		{
			name: "fail after max retries",
			op: func() error {
				return errors.New("persistent error")
			},
			errMsgFormat:  "operation failed: %v, %w",
			args:          []interface{}{"test"},
			expectedErr:   errors.New("operation failed: test, persistent error"),
			expectedCalls: 4, // 1 initial + 3 retries
		},
		{
			name: "fail after max retries and with no args",
			op: func() error {
				return errors.New("persistent error")
			},
			errMsgFormat:  "operation failed: %w",
			args:          nil,
			expectedErr:   errors.New("operation failed: persistent error"),
			expectedCalls: 4, // 1 initial + 3 retries
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &PageWatcherIns{}
			ctx := context.Background()

			// Track actual calls
			callCount := 0
			wrappedOp := func() error {
				callCount++
				return tt.op()
			}

			var err error
			if tt.args == nil {
				err = p.retryOperation(ctx, wrappedOp, tt.errMsgFormat)
			} else {
				err = p.retryOperation(ctx, wrappedOp, tt.errMsgFormat, tt.args...)
			}

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr.Error(), err.Error())
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, tt.expectedCalls, callCount)
		})
	}
}

// TestGetRootCauseFromDatadog tests the getRootCauseFromDatadog function
func TestGetRootCauseFromDatadog(t *testing.T) {
	t.Skip("manual test - requires real Datadog incident")

	// This test requires a real Datadog incident ID and proper configuration
	// to access the Datadog API.

	// Setup
	setUp()
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")

	jiraClient, err := jira.NewIssueRepository(cfg.CsPageWatcher.JiraEmail, cfg.CsPageWatcher.JiraToken)
	if err != nil {
		t.Fatalf("failed to create jira client: %v", err)
	}
	slackClient := slack.NewClient(cfg.CsPageWatcher.SlackToken)

	pageWatcher := &PageWatcherIns{
		cfg:          cfg,
		jiraClient:   jiraClient,
		slackClient:  slackClient,
		csPageRepo:   entity.NewCsPageReaderWriter(),
		locker:       pkg.NewLocalLocker(),
		jiraReminder: nil,
	}

	// Test with a real incident ID that has a root cause
	ctx := context.Background()
	incidentID := "110" // Replace with a real incident ID
	rootCause := pageWatcher.getRootCauseFromDatadog(ctx, incidentID)

	t.Logf("Root cause: %s", rootCause)
	assert.NotEmpty(t, rootCause)
	assert.NotEqual(t, "root cause is not filled", rootCause)
}

// TestUpdateJiraAndCloseTicket tests the updateJiraAndCloseTicket function
func TestUpdateJiraAndCloseTicket(t *testing.T) {
	t.Skip("manual test - requires real Jira ticket")

	// This test requires a real Jira ticket that can be updated and closed
	// without causing issues in the production environment.

	// Setup
	setUp()
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")

	jiraClient, err := jira.NewIssueRepository(cfg.CsPageWatcher.JiraEmail, cfg.CsPageWatcher.JiraToken)
	assert.NoError(t, err)

	pageWatcher := &PageWatcherIns{
		cfg:        cfg,
		jiraClient: jiraClient,
	}

	// Test with a real Jira ticket
	ctx := context.Background()
	jiraTicket := "CS-32206"
	rootCause := "代码中有一段逻辑没有兼容这个 book by pet family 白名单，导致不符合预期"

	err = pageWatcher.updateJiraAndCloseTicket(ctx, jiraTicket, rootCause)
	assert.NoError(t, err)
}
