package slack

import (
	"fmt"
	"regexp"
	"strings"

	"github.com/samber/lo"
	"github.com/slack-go/slack"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type Client interface {
	AddMembersToChannel(channelID string, memberEmails []string) error
	AddEmojiToMessage(channelID string, messageTS string, emoji string) error
	Join<PERSON>hannel(channelID string) error
	CreateChannel(channelName string) (*slack.Channel, error)
	ArchiveChannel(channelID string) error
	SendMessage(channelID string, message string) (timestampTS string, err error)
	SendMessageToThread(channelID string, messageTS string, message string) error
	SendMessageToPerson(memberEmail string, message string, picUrls ...string) (err error)
	SendMessageToPersonWithOKButton(
		memberEmail string,
		message string,
		buttonValue string,
		picUrls ...string,
	) (err error)
	GetUserEmailsByUsergroups(usergroupIDs []string) ([]string, error)

	LookUpByEmail(emailList []string) (userIDList []string)
}

// API is an interface that wraps the slack API methods we use
type API interface {
	GetUserByEmail(email string) (*slack.User, error)
	InviteUsersToConversation(channelID string, users ...string) (*slack.Channel, error)
	AddReaction(name string, item slack.ItemRef) error
	JoinConversation(channelID string) (*slack.Channel, string, []string, error)
	PostMessage(channelID string, options ...slack.MsgOption) (string, string, error)
	OpenConversation(params *slack.OpenConversationParameters) (*slack.Channel, bool, bool, error)
	CreateConversation(params slack.CreateConversationParams) (*slack.Channel, error)
	ArchiveConversation(channelID string) error
	GetUserGroupMembers(userGroup string) ([]string, error)
	GetUserInfo(userID string) (*slack.User, error)
}

type slackClient struct {
	api API
}

func NewClient(token string) Client {
	return &slackClient{
		api: slack.New(token),
	}
}

func (s *slackClient) AddMembersToChannel(channelID string, memberEmails []string) error {
	userIDList := s.LookUpByEmail(memberEmails)

	// 过滤掉UNKNOWN_USER的userIDList
	userIDList = lo.Filter(userIDList, func(userID string, _ int) bool {
		return userID != "UNKNOWN_USER"
	})

	if len(userIDList) == 0 {
		// 如果没有有效用户，直接返回nil而不是错误
		return nil
	}

	_, err := s.api.InviteUsersToConversation(channelID, userIDList...)
	if err != nil {
		return fmt.Errorf("AddMembersToChannel: failed to add members to channel %s: %w", channelID, err)
	}

	return nil
}

func (s *slackClient) SendMessageToThread(channelID string, messageTS string, message string) error {
	_, _, err := s.api.PostMessage(
		channelID,
		slack.MsgOptionText(message, false),
		slack.MsgOptionTS(messageTS),
	)
	if err != nil {
		return fmt.Errorf("failed to send message to thread %s in channel %s: %w", messageTS, channelID, err)
	}

	return nil
}

func (s *slackClient) AddEmojiToMessage(channelID string, messageTS string, emoji string) error {
	err := s.api.AddReaction(emoji, slack.ItemRef{
		Channel:   channelID,
		Timestamp: messageTS,
	})
	if err != nil {
		return fmt.Errorf("failed to add emoji %s to message %s in channel %s: %w", emoji, messageTS, channelID, err)
	}

	return nil
}

func (s *slackClient) JoinChannel(channelID string) error {
	_, _, _, err := s.api.JoinConversation(channelID)
	if err != nil {
		return fmt.Errorf("failed to join channel %s: %w", channelID, err)
	}

	return nil
}

func (s *slackClient) LookUpByEmail(emailList []string) (userIDList []string) {
	userIDList = make([]string, 0, len(emailList))
	for _, email := range emailList {
		if email == "" {
			userIDList = append(userIDList, "UNKNOWN_USER")

			continue
		}
		user, err := s.api.GetUserByEmail(email)
		if err != nil {
			log.Errorf("failed to look up user by email %s: %v", email, err)
			userIDList = append(userIDList, "UNKNOWN_USER")

			continue
		}
		userIDList = append(userIDList, user.ID)
	}

	return userIDList
}

func (s *slackClient) SendMessage(channelID string, message string) (timestampTS string, err error) {
	_, timestampTS, err = s.api.PostMessage(channelID, slack.MsgOptionText(message, false))
	if err != nil {
		return "", fmt.Errorf("failed to send message to channel %s: %w", channelID, err)
	}

	return timestampTS, nil
}

func (s *slackClient) SendMessageToPerson(memberEmail string, message string, picURLs ...string) (err error) {
	userIDList := s.LookUpByEmail([]string{memberEmail})
	if len(userIDList) == 0 {
		return fmt.Errorf("SendMessageToPerson: no user found for email %s", memberEmail)
	}

	// Open a direct message channel with the user
	channel, _, _, err := s.api.OpenConversation(&slack.OpenConversationParameters{
		Users: []string{userIDList[0]},
	})
	if err != nil {
		return fmt.Errorf("SendMessageToPerson: failed to open IM channel with user %s: %w", memberEmail, err)
	}

	var msgOptions []slack.MsgOption

	// If there are picUrls, we need to add both the text and image blocks
	if len(picURLs) > 0 {
		// Create a text block for the message
		textBlock := slack.NewTextBlockObject("mrkdwn", message, false, false)

		// Create image blocks for each picture
		var blocks []slack.Block
		blocks = append(blocks, slack.NewSectionBlock(textBlock, nil, nil)) // Add the main message as a section block

		for i, picURL := range picURLs {
			// Create a unique block ID for each image
			blockID := fmt.Sprintf("image_block_%d_%s", i, strings.ReplaceAll(picURL, "/", "_"))
			imageBlock := slack.NewImageBlock(
				picURL,
				"Attached image",           // alt text
				strings.Trim(blockID, "_"), // block id
				slack.NewTextBlockObject("plain_text", fmt.Sprintf("Image %d", i+1), false, false), // title
			)
			blocks = append(blocks, imageBlock)
		}
		msgOptions = append(msgOptions, slack.MsgOptionBlocks(blocks...))
	} else {
		// If no images, just send the text message as before
		msgOptions = append(msgOptions, slack.MsgOptionText(message, false))
	}

	_, _, err = s.api.PostMessage(channel.ID, msgOptions...)
	if err != nil {
		return fmt.Errorf("failed to send message to person %s: %w", memberEmail, err)
	}

	return nil
}

func (s *slackClient) SendMessageToPersonWithOKButton(
	memberEmail string,
	message string,
	buttonValue string,
	picUrls ...string,
) (err error) {
	userIDList := s.LookUpByEmail([]string{memberEmail})
	if len(userIDList) == 0 {
		return fmt.Errorf("SendMessageToPersonWithOKButton: no user found for email %s", memberEmail)
	}

	// Open a direct message channel with the user
	channel, _, _, err := s.api.OpenConversation(&slack.OpenConversationParameters{
		Users: []string{userIDList[0]},
	})
	if err != nil {
		return fmt.Errorf("SendMessageToPersonWithOKButton: failed to open IM channel with user %s: %w",
			memberEmail, err)
	}

	var blocks []slack.Block

	// Add the message as a section block
	textBlock := slack.NewTextBlockObject("mrkdwn", message, false, false)
	blocks = append(blocks, slack.NewSectionBlock(textBlock, nil, nil))

	// Add image blocks if there are picUrls
	for i, picURL := range picUrls {
		// Create a unique block ID for each image
		blockID := fmt.Sprintf("image_block_%d_%s", i, strings.ReplaceAll(picURL, "/", "_"))
		imageBlock := slack.NewImageBlock(
			picURL,
			"Attached image",           // alt text
			strings.Trim(blockID, "_"), // block id
			slack.NewTextBlockObject("plain_text", fmt.Sprintf("Image %d", i+1), false, false), // title
		)
		blocks = append(blocks, imageBlock)
	}

	// Create an actions block containing the OK button with the provided button value
	buttonText := slack.NewTextBlockObject("plain_text", "OK", false, false)
	buttonElement := slack.NewButtonBlockElement("", buttonValue, buttonText) // action_id, value, text
	actionsBlock := slack.NewActionBlock("ok_action_block", buttonElement)
	blocks = append(blocks, actionsBlock)

	_, _, err = s.api.PostMessage(channel.ID, slack.MsgOptionBlocks(blocks...))
	if err != nil {
		return fmt.Errorf("failed to send message with OK button to person %s: %w", memberEmail, err)
	}

	return nil
}

func (s *slackClient) GetUserEmailsByUsergroups(usergroupIDs []string) ([]string, error) {
	var allEmails []string

	for _, usergroupID := range usergroupIDs {
		// Get all user IDs in this user group
		userIDs, err := s.api.GetUserGroupMembers(usergroupID)
		if err != nil {
			return nil, fmt.Errorf(
				"GetUserEmailsByUsergroups: failed to get members for usergroup %s: %w",
				usergroupID,
				err,
			)
		}

		// Get user info for each user ID to get their email
		for _, userID := range userIDs {
			userInfo, err := s.api.GetUserInfo(userID)
			if err != nil {
				log.Errorf(
					"GetUserEmailsByUsergroups: failed to get user info for user %s in usergroup %s: %v",
					userID,
					usergroupID,
					err,
				)

				continue // Skip this user and continue with the next one
			}

			if userInfo != nil && userInfo.Profile.Email != "" {
				allEmails = append(allEmails, userInfo.Profile.Email)
			}
		}
	}

	return allEmails, nil
}

// CreateChannel creates a new Slack channel with the given name
func (s *slackClient) CreateChannel(channelName string) (*slack.Channel, error) {
	// Sanitize the channel name to comply with Slack's naming rules
	sanitizedName := s.sanitizeChannelName(channelName)

	channel, err := s.api.CreateConversation(slack.CreateConversationParams{
		ChannelName: sanitizedName,
		IsPrivate:   false,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create channel %s: %w", sanitizedName, err)
	}

	return channel, nil
}

// ArchiveChannel archives a Slack channel with the given ID
func (s *slackClient) ArchiveChannel(channelID string) error {
	if err := s.api.ArchiveConversation(channelID); err != nil {
		return fmt.Errorf("failed to archive channel %s: %w", channelID, err)
	}

	return nil
}

// sanitizeChannelName cleans a string to make it suitable for use as a Slack channel name
// Slack channel names must:
// - Be lowercase
// - Contain only letters, numbers, hyphens, and underscores
// - Be no longer than 80 characters
// - Not start or end with a hyphen or underscore
// - Not have consecutive hyphens or underscores
func (s *slackClient) sanitizeChannelName(name string) string {
	const unknownChannelName = "unknown"

	if name == "" {
		return unknownChannelName
	}

	// Convert to lowercase
	name = strings.ToLower(name)

	// Replace spaces with hyphens
	name = strings.ReplaceAll(name, " ", "-")

	// Remove any characters that are not letters, numbers, hyphens, or underscores
	reg := regexp.MustCompile("[^a-z0-9_-]+")
	name = reg.ReplaceAllString(name, "")

	// Replace multiple consecutive hyphens or underscores with a single one
	reg = regexp.MustCompile("[-_]{2,}")
	name = reg.ReplaceAllString(name, "-")

	// Remove leading/trailing hyphens or underscores
	name = strings.Trim(name, "-_")

	// Limit to 80 characters
	if len(name) > 80 {
		name = name[:80]
		// Make sure we don't end with a hyphen or underscore after truncation
		name = strings.Trim(name, "-_")
	}

	// If the result is empty, return a default value
	if name == "" {
		return unknownChannelName
	}

	return name
}
