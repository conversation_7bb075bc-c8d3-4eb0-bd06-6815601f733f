# CS Page Watcher - 项目上下文

本文档是这个项目简单的技术索引，用于给AI提示，并由AI进行维护。AI不能改动结构，但可以增补内容。
AI需要按格式修订文档，格式层级如下：

```text
## 核心组件
### 每个组件的信息
标注目录或文件，然后按文件-结构-方法的级别进行展示，包含配置，说明，核心特性
```

## 概述

CS Page Watcher 是一个监控与客户支持相关的 Jira 工单并根据工单属性执行自动化操作的服务。该系统可以触发 Datadog 事件、创建 Slack 频道、将工单分配给适当的团队以及处理数据导入任务。

## 系统架构

### 数据流向

```text
Jira 工单 → SLA 评估 → 过滤分组 → Slack 通知
    ↓           ↓          ↓         ↓
 工单监控    优先级计算   团队分配   用户提醒
    ↓           ↓          ↓         ↓
Datadog事件  时间计算    频道映射   消息发送
```

### 核心流程

1. **工单监控**: 定时从 Jira 获取新建或更新的工单
2. **SLA 评估**: 根据优先级和创建时间计算 SLA 状态
3. **智能过滤**: 按违反状态、时间窗口、优先级进行多层过滤
4. **团队分配**: 基于组件映射自动分配到对应团队
5. **通知发送**: 通过 Slack 发送结构化提醒消息
6. **事件管理**: 与 Datadog 集成进行事件生命周期管理

### 配置管理

- **三层配置架构**: 本地基础配置 + 本地业务配置 + Nacos 动态配置
- **热更新支持**: Nacos 配置变更无需重启服务
- **类型安全**: 使用强类型定义避免配置错误

## 核心组件

### 配置 (configloader/)

配置方式：支持三层配置架构

- 本地TRPC配置 (config.yaml)，业务无关
- 本地业务配置 (cs_page_watcher.yaml)  
- Nacos 动态配置 (cs-page-watcher.yaml)

**文件**: configloader.go

- 结构体: Config - 主配置结构，从 cs_page_watcher.yaml 加载基础设施配置
- 方法: Init() - 初始化本地配置
- 方法: InitNacosConfigAndGet() - 初始化并获取 Nacos 配置

**文件**: nacos.go

- 结构体: CSPageConfig - Nacos 动态配置结构，包含业务规则和映射关系 (ExampleKey, ComponentsSquadsMapping, RefUser, DataImportUserEmails, SquadSlackChannelMapping, TeamSchedules)
- 结构体: GlobalNacosCsPageConfig - 全局 Nacos 配置实例，支持热更新
- 结构体: Schedule - 团队时间表配置 (Name, ScheduleID, AdminTaskScheduleID, TeamHandle)
- 方法: Load() - 获取当前配置
- 方法: StartWatch() - 启动配置监听和动态更新

**配置字段说明**:

- components_squads_mapping: Jira 组件到团队的映射，用于自动分配工单
- squad_slack_channel_mapping: 团队到 Slack 频道 ID 的映射
- team_schedules: 团队时间表配置，包含 Datadog 调度 ID 和团队句柄（从 global.TeamSchedules 迁移）
- ref_user: 参考用户列表
- data_import_user_emails: 数据导入任务相关用户邮箱
- google_credential_json: Google 服务账户凭据 JSON 字符串
- google_project_id: Google 项目 ID
- google_bucket_name: Google 云存储桶名称
- product_update_sheet_id: 产品更新记录的 Google Sheets ID
- product_update_sheet_name: 产品更新记录的工作表名称

### 主服务 (service/page_watcher.go)

**结构体**: PageWatcherIns - CS Page Watcher 服务的主要实现，实现了 pb.CSPageWatcherServiceServer 接口

**核心方法**:

- MonitorAndPage() - 主监控功能，持续监控 Jira 工单状态
- TriggerPage() - 从 Jira 工单触发页面，为符合条件的工单创建 Datadog 事件
- CompleteIncident() - 完成事件并更新 Jira 工单状态
- CompleteJira() - 完成没有关联事件的 Jira 工单
- TriggerAdminTask() - 将工单分配给适当的负责人，根据PST时间分配给固定人员(<EMAIL> 或 <EMAIL>)
- sendAdminTaskNotification() - 向被分配的负责人发送 Slack 直接消息通知，而不是发送到共享频道
- RunTask() - 执行定时任务，支持任务类型：
  - Open Tasks: Bugs - 开放的 Bug 工单提醒
  - Overdue Open Tasks: Bugs - 逾期的 Bug 工单提醒  
  - Open Tasks: AdminTasks - 开放的管理任务提醒
  - Overdue Open Tasks: AdminTasks - 逾期的管理任务提醒
  - Data Import Task - 数据导入任务处理
  - DM Subtask Reminder - DM 项目子任务提醒
  - ProductUpdate - 产品更新通知任务
  - ProductUpdateOK - 产品更新确认记录任务
- runDataImportTask() - 处理数据导入任务，为数据导入任务创建 Slack 频道并通知相关人员
- runProductUpdateTask() - 处理产品更新通知，发送带OK按钮的消息给用户组成员
- runProductUpdateOKTask() - 处理产品更新确认，记录点击OK按钮的用户信息到Google Sheets
- dmSubTaskReminder() - 处理 DM 项目子任务状态更新提醒，自动发送状态变更通知

**核心特性**:

- 事件管理：与 Datadog 集成进行事件创建和管理
- 团队分配：根据组件自动将工单分配给适当的团队
- SLA 监控：为开放和逾期的工单安排提醒
- Slack 集成：在 Slack 中创建频道和通知
- Google Sheets 集成：记录操作数据到 Google Sheets
- 数据导入任务：特殊处理数据导入工单，包括频道创建
- Jira 自动化：在工作流中自动更新 Jira 工单
- 产品更新通知：发送带OK按钮的消息并跟踪用户确认
- DM 子任务提醒：当 DM 项目子任务状态更新时自动发送 Slack 提醒
- 动态配置管理：支持 Nacos 配置热更新，业务规则可实时调整

**Protobuf 服务定义**: 实现 cs_page_watcher.proto 中定义的 CSPageWatcherService

### 产品更新通知 (service/product_update_notify.go)

**功能**: 处理产品更新通知任务

**核心方法**:

- runProductUpdateTask() - 解析用户组，获取用户邮箱，并发送带OK按钮和图片的消息给所有用户

**特点**:

- 从RunTask接收到的参数中解析text、file URLs和usergroups
- 使用productSlackClient发送消息
- 支持在按钮值中嵌入源消息时间戳(origin_ts)

### 产品更新确认记录 (service/product_update_ok.go)

**功能**: 处理产品更新确认按钮点击事件

**核心方法**:

- runProductUpdateOKTask() - 当用户点击OK按钮时，记录确认信息到Google Sheets

**记录字段**:

- origin_ts: 源消息时间戳
- username: 点击按钮的用户名
- message.text: 消息文本内容
- image: 图片URL
- insert_time: 记录插入时间

### DM 子任务提醒 (service/dm_reminder.go)

**方法**: dmSubTaskReminder - 当 DM 项目子任务状态更新时发送 Slack 提醒

### 全局常量 (global/)

**文件**: global.go

- 常量: Jira 自定义字段名称 (T1OrGeneral, IssuePriority 等)
- 类型: Priority - 类型安全的优先级定义 (PriorityP0-PriorityP5)
- 常量: Jira 优先级层级 (TierT1, TierOther)
- 常量: Slack 表情符号常量
- 常量: Slack 频道 ID 常量 (AdminTaskChannelID)
- 常量: 联系人映射 (MoegoContacts)
- 常量: Squad 常量定义
- ~~常量: 团队时间表和映射 (TeamSchedules)~~ **已迁移到 Nacos 配置**

### 数据模型 (repo/entity/)

**文件**: cs_page.go

- 结构体: CsPage - 表示 cs_page 表 (cs_page_jira_ticket, datadog_incident_id, incident_slack_channel_id, t1_notify_message_timestamp, create_time)
- 接口: CsPageReaderWriter - cs_page 表的读写接口
- 结构体: CsPageReaderWriterIns - 接口实现

**文件**: cs_data_import_task.go

- 结构体: CsDataImportTask - 表示 cs_data_import_task 表 (jira_key, slack_channel_id, create_time)
- 接口: CsDataImportTaskReaderWriter - cs_data_import_task 表的读写接口
- 结构体: CsDataImportTaskReaderWriterIns - 接口实现

### 外部集成-Jira (repo/jira/)

**文件**: issue_repository.go

- 接口: IssueRepository - Jira 操作接口
- 结构体: IssueRepositoryIns - 使用 go-jira 库的实现
- 结构体: Issue - Jira 工单数据结构 (ID, Key, Summary, Status, Created, Updated, Assignee, Reporter, Parent, T1OrGeneral, IssuePriority, CustomerStage, Components, JiraSquad, SLABreach, LogoName, LocationName, DevEngineer, ResolutionTimeCustom, CreatedByCustom, FeatureDomains)
- 结构体: Parent - Jira 工单父问题 (ID, Key)
- 方法: GetIssueDetails() - 获取工单详细信息
- 方法: GetNewOrUpdatedBugTickets() - 获取符合条件的工单
- 方法: CloseIssue() - 关闭工单
- 方法: SetAssignee() - 分配工单
- 方法: SetCauseAndSolution() - 设置原因和解决方案
- 方法: SetFeatureDomains() - 设置功能域
- 方法: SearchJql() - JQL 搜索，支持分页，使用 Jira REST API v3，修复分页问题可获取所有符合条件工单

**重要更新**:

- 优先级类型安全改进：IssuePriority 字段改用 global.Priority 类型替代 string
- 添加优先级常量：PriorityP0-PriorityP5 (P0-Block 到 P5-Low)
- Jira 搜索分页修复：使用正确的 Jira API v3 端点和 nextPageToken 分页机制

### 外部集成-Slack (repo/slack/)

**文件**: client.go

- 接口: Client - Slack 操作接口
- 结构体: slackClient - 使用 slack-go 库的实现
- 方法: CreateChannel() - 创建新的 Slack 频道
- 方法: AddMembersToChannel() - 向频道添加成员
- 方法: SendMessage() - 向频道发送消息
- 方法: SendMessageToThread() - 向线程发送消息
- 方法: SendMessageToPerson() - 发送直接消息，支持附加图片
- 方法: SendMessageToPersonWithOKButton() - 发送带OK按钮的直接消息，支持附加图片和自定义按钮值
- 方法: GetUserEmailsByUsergroups() - 根据用户组ID列表获取组中所有用户的邮箱
- 方法: AddEmojiToMessage() - 为消息添加表情符号反应
- 方法: JoinChannel() - 加入频道
- 方法: LookUpByEmail() - 通过邮箱查找用户 ID，支持批量查询
- 方法: ArchiveChannel() - 归档 Slack 频道

**Slack 应用权限配置**:

- `usergroups:read` - 用于调用 GetUserGroupMembers API，获取用户组成员列表
- `users:read` - 用于调用 GetUserInfo API，获取用户信息
- `users:read.email` - 获取用户的邮箱地址
- `channels:read`, `groups:read`, `im:read`, `mpim:read` - 用于频道相关操作
- `chat:write` - 用于发送消息
- `reactions:write` - 用于添加表情符号

### 外部集成-Google (repo/google/)

**文件**: sheet.go

- 接口: SheetClient - Google Sheets 操作接口
- 结构体: sheetClient - 使用 Google Sheets API 的实现
- 方法: NewSheetClient() - 创建 Google Sheets 客户端，接受 JSON 凭证
- 方法: InsertRow() - 在指定工作表中插入一行数据

**功能**:

- 用于记录产品更新确认数据到 Google Sheets
- 支持通过 JSON 凭证进行身份验证
- 用于 ProductUpdateOK 任务的数据持久化

### 核心流程 (service/)

**新增任务类型**:

- ProductUpdate - 在 n8n 触发产品更新通知时，解析用户组并发送带OK按钮的消息
- ProductUpdateOK - 当用户点击OK按钮时，记录确认信息到 Google Sheets

### 外部集成-Datadog (repo/datadog/)

**文件**: incident_triger.go

- 接口: IncidentGateway - Datadog 操作接口
- 结构体: IncidentGatewayImpl - 实现类，通过依赖注入接收 nacos 配置
- 方法: TriggerIncident() - 触发事件
- 方法: GetRootCause() - 获取根本原因
- 构造函数: NewIncidentGateway(cfg, slackClient, csPageReaderWriter, nacosCsConfig)

**重要更新**: TeamSchedules 配置迁移

- 原位置：global/global.go 中的 TeamSchedules 变量
- 新位置：Nacos 配置中的 team_schedules 字段
- 访问方式：通过依赖注入的 nacosCsConfig.Load().TeamSchedules 替代 global.TeamSchedules
- 优势：支持热更新，无需重启服务即可调整团队时间表配置

### 逻辑组件 (logic/)

**文件**: jira_incident_evaluator.go

- 结构体: JiraIncidentEvaluator - 评估 Jira 工单以确定是否应触发事件

**文件**: jiraslareminder/jirasla_reminder.go

- 接口: SLAReminder - SLA 评估接口
- 结构体: slaReminder - SLA 评估实现
- 结构体: SLAResult - SLA 评估结果 (JiraKey, IsSLABreached, TimeUntilBreach, DevEngineerEmail, AssigneeEmail)
- 方法: EvaluateJiraTickets() - 评估工单 SLA 状态
- 方法: getSLAByPriority() - 根据优先级获取 SLA 时间限制

**SLA 计算逻辑**:

- 基于工单创建时间和优先级计算 SLA 到期时间
- 支持违反状态检测和剩余时间计算
- 提取开发工程师和负责人邮箱信息

### SLA 提醒系统 (service/jira_sla_reminder.go)

**结构体**: ScheduledJiraReminder - 处理 SLA 提醒任务

**核心方法**:

- Remind() - 主要提醒入口，处理工单类型、任务名称、关闭状态和优先级过滤
- remind() - 核心提醒逻辑，包含 SLA 评估、过滤和消息分组
- sendSlackReminders() - 发送 Slack 提醒消息到各团队频道（或 AdminTask 专用频道）
- sendTaskWithThread() - 发送任务头部消息并在线程中发送详细信息
- sendAdminTaskSlackReminders() - 发送 AdminTask 的 Slack 提醒消息到专用频道
- filterSLAResults() - 过滤即将违反 SLA 的工单（24小时内）
- filterSLABreakResults() - 过滤已违反 SLA 的工单
- filterByPriority() - 按优先级过滤工单
- groupMessagesBySquad() - 按团队分组消息并按优先级排序
- getEmailToUserIDMap() - 获取邮箱到 Slack 用户 ID 的映射

**SLA 规则** (logic/jiraslareminder/):

- P0-Block: 3小时
- P1-Critical: 24小时  
- P2-High: 72小时
- P3-Moderate: 168小时（7天）
- P4-Minor: 360小时（15天）
- P5-Low: 720小时（30天）

**核心特性**:

- 双重过滤模式：支持 SLA 违反提醒和即将违反提醒
- 优先级过滤：可按指定优先级列表过滤工单
- 智能分组：按团队自动分组，无团队工单发送到默认频道（AdminTask 除外）
- 消息排序：团队内消息按优先级排序（P0 > P1 > P2 > P3 > P4 > P5）
- 用户提及：自动 @ 相关开发工程师和负责人（AdminTask 会额外 @ 指定人员）
- 线程组织：使用 Slack 线程保持频道整洁
- 动态配置：支持从 Nacos 获取团队频道映射
- 类型安全：使用 global.Priority 类型替代 string
- 常量管理：关键 ID 和配置项定义在 global.go 中便于维护

**副作用分析**:

- 有副作用：LookUpByEmail (Slack API)、日志记录 (IO)
- 纯逻辑：SLA 评估、过滤、分组、消息格式化

### 测试覆盖 (service/jira_sla_reminder_remind_test.go, mocks/)

**测试结构**:

- **Mock 组件** (mocks/slack_client_mock.go): MockSlackClient - Slack 客户端 Mock 实现
- **测试用例**: TestScheduledJiraReminder_remind - remind 函数的完整测试套件

**测试场景**:

1. **SLA 违反过滤测试**: 验证已违反 SLA 的工单正确过滤和分组
2. **常规 SLA 过滤测试**: 验证 24 小时内将违反 SLA 的工单被包含
3. **无结果过滤测试**: 验证不符合条件的工单被正确排除
4. **空工单列表测试**: 验证空输入的边界条件处理
5. **优先级过滤测试**: 验证按优先级过滤功能的正确性

**测试特点**:

- 使用真实 SLA 计算逻辑 (jiraslareminder.NewSLAReminder())
- Mock 外部依赖 (Slack API 调用)
- 基于时间的测试数据构造
- 完整的断言验证 (消息内容、分组结果、用户提及)
- 类型安全验证 (使用 global.Priority 常量)
