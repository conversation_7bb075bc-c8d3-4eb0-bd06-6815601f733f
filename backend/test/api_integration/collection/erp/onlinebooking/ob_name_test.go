package onlinebooking

import (
	"net/http"
	"testing"

	servicemodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/service/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/svc"
	"github.com/stretchr/testify/suite"
)

type OBNameTestSuite struct {
	suite.Suite
	// 测试内部 svc 接口时，使用 svc 包里的 context
	svc.Context
}

func (s *OBNameTestSuite) SetupSuite() {
	s.Context.Setup(&s.Suite)
}

func (s *OBNameTestSuite) TearDownSuite() {
	s.Context.Teardown()
}

func (s *OBNameTestSuite) TestGetCompanyIDByOBName() {
	// 对于老的 java server 服务，内部接口是用 http 协议调用的
	// 使用 svc.Context 的 NewHttpServerRequest 方法，会自动创建一个给定服务的内部 http 请求
	result := &servicemodel.CompanyBusinessIdDTO{}
	resp, err := s.NewHttpServerRequest(svc.MoegoServerGrooming).
		SetMethodPath(http.MethodGet, "/service/grooming/bookOnline/client/companyBusinessId").
		AddQuery("obName", "PeterSuperPet").
		SetResult(result).
		Send()
	s.Require().NoError(err)
	s.Require().True(resp.IsSuccess())
	s.Require().NotNil(result.CompanyId)
	s.Require().Equal(int64(100870), *result.CompanyId)
}

func TestOBNameTestSuite(t *testing.T) {
	suite.Run(t, new(OBNameTestSuite))
}