// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/fulfillment/v1/appointment_service.proto

package fulfillmentpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AppointmentService_CreateAppointment_FullMethodName    = "/backend.proto.fulfillment.v1.AppointmentService/CreateAppointment"
	AppointmentService_ListAppointment_FullMethodName      = "/backend.proto.fulfillment.v1.AppointmentService/ListAppointment"
	AppointmentService_UpdateAppointment_FullMethodName    = "/backend.proto.fulfillment.v1.AppointmentService/UpdateAppointment"
	AppointmentService_GetAppointmentByIDs_FullMethodName  = "/backend.proto.fulfillment.v1.AppointmentService/GetAppointmentByIDs"
	AppointmentService_ListCustomerPackages_FullMethodName = "/backend.proto.fulfillment.v1.AppointmentService/ListCustomerPackages"
)

// AppointmentServiceClient is the client API for AppointmentService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 预约服务
type AppointmentServiceClient interface {
	// 创建预约
	// (-- api-linter: core::0133::response-message-name=disabled
	//
	//	aip.dev/not-precedent: CreateAppointmentResponse is appropriate for this use case --)
	CreateAppointment(ctx context.Context, in *CreateAppointmentRequest, opts ...grpc.CallOption) (*CreateAppointmentResponse, error)
	// 列出预约
	ListAppointment(ctx context.Context, in *ListAppointmentRequest, opts ...grpc.CallOption) (*ListAppointmentResponse, error)
	// 更新预约
	// (-- api-linter: core::0134::response-message-name=disabled
	//
	//	aip.dev/not-precedent: UpdateAppointmentResponse is appropriate for this use case --)
	UpdateAppointment(ctx context.Context, in *UpdateAppointmentRequest, opts ...grpc.CallOption) (*UpdateAppointmentResponse, error)
	// 根据ID列表获取预约信息
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: GetAppointmentByIdsResponse is appropriate for this use case --)
	//
	// (-- api-linter: core::0136::prepositions=disabled
	//
	//	aip.dev/not-precedent: GetAppointmentByIds is appropriate for this use case --)
	GetAppointmentByIDs(ctx context.Context, in *GetAppointmentByIDsRequest, opts ...grpc.CallOption) (*GetAppointmentByIDsResponse, error)
	// 列出客户套餐
	// (-- api-linter: core::0132::request-parent-required=disabled
	//
	//	aip.dev/not-precedent: business_id is used as parent in this context --)
	//
	// (-- api-linter: core::0158::request-page-token-field=disabled
	//
	//	aip.dev/not-precedent: 不需要page_token，不分页 --)
	//
	// (-- api-linter: core::0158::request-page-size-field=disabled
	//
	//	aip.dev/not-precedent: 不需要page_size，不分页 --)
	ListCustomerPackages(ctx context.Context, in *ListCustomerPackagesRequest, opts ...grpc.CallOption) (*ListCustomerPackagesResponse, error)
}

type appointmentServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAppointmentServiceClient(cc grpc.ClientConnInterface) AppointmentServiceClient {
	return &appointmentServiceClient{cc}
}

func (c *appointmentServiceClient) CreateAppointment(ctx context.Context, in *CreateAppointmentRequest, opts ...grpc.CallOption) (*CreateAppointmentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateAppointmentResponse)
	err := c.cc.Invoke(ctx, AppointmentService_CreateAppointment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) ListAppointment(ctx context.Context, in *ListAppointmentRequest, opts ...grpc.CallOption) (*ListAppointmentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAppointmentResponse)
	err := c.cc.Invoke(ctx, AppointmentService_ListAppointment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) UpdateAppointment(ctx context.Context, in *UpdateAppointmentRequest, opts ...grpc.CallOption) (*UpdateAppointmentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateAppointmentResponse)
	err := c.cc.Invoke(ctx, AppointmentService_UpdateAppointment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) GetAppointmentByIDs(ctx context.Context, in *GetAppointmentByIDsRequest, opts ...grpc.CallOption) (*GetAppointmentByIDsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAppointmentByIDsResponse)
	err := c.cc.Invoke(ctx, AppointmentService_GetAppointmentByIDs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) ListCustomerPackages(ctx context.Context, in *ListCustomerPackagesRequest, opts ...grpc.CallOption) (*ListCustomerPackagesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCustomerPackagesResponse)
	err := c.cc.Invoke(ctx, AppointmentService_ListCustomerPackages_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AppointmentServiceServer is the server API for AppointmentService service.
// All implementations must embed UnimplementedAppointmentServiceServer
// for forward compatibility.
//
// 预约服务
type AppointmentServiceServer interface {
	// 创建预约
	// (-- api-linter: core::0133::response-message-name=disabled
	//
	//	aip.dev/not-precedent: CreateAppointmentResponse is appropriate for this use case --)
	CreateAppointment(context.Context, *CreateAppointmentRequest) (*CreateAppointmentResponse, error)
	// 列出预约
	ListAppointment(context.Context, *ListAppointmentRequest) (*ListAppointmentResponse, error)
	// 更新预约
	// (-- api-linter: core::0134::response-message-name=disabled
	//
	//	aip.dev/not-precedent: UpdateAppointmentResponse is appropriate for this use case --)
	UpdateAppointment(context.Context, *UpdateAppointmentRequest) (*UpdateAppointmentResponse, error)
	// 根据ID列表获取预约信息
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: GetAppointmentByIdsResponse is appropriate for this use case --)
	//
	// (-- api-linter: core::0136::prepositions=disabled
	//
	//	aip.dev/not-precedent: GetAppointmentByIds is appropriate for this use case --)
	GetAppointmentByIDs(context.Context, *GetAppointmentByIDsRequest) (*GetAppointmentByIDsResponse, error)
	// 列出客户套餐
	// (-- api-linter: core::0132::request-parent-required=disabled
	//
	//	aip.dev/not-precedent: business_id is used as parent in this context --)
	//
	// (-- api-linter: core::0158::request-page-token-field=disabled
	//
	//	aip.dev/not-precedent: 不需要page_token，不分页 --)
	//
	// (-- api-linter: core::0158::request-page-size-field=disabled
	//
	//	aip.dev/not-precedent: 不需要page_size，不分页 --)
	ListCustomerPackages(context.Context, *ListCustomerPackagesRequest) (*ListCustomerPackagesResponse, error)
	mustEmbedUnimplementedAppointmentServiceServer()
}

// UnimplementedAppointmentServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAppointmentServiceServer struct{}

func (UnimplementedAppointmentServiceServer) CreateAppointment(context.Context, *CreateAppointmentRequest) (*CreateAppointmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) ListAppointment(context.Context, *ListAppointmentRequest) (*ListAppointmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) UpdateAppointment(context.Context, *UpdateAppointmentRequest) (*UpdateAppointmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) GetAppointmentByIDs(context.Context, *GetAppointmentByIDsRequest) (*GetAppointmentByIDsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppointmentByIDs not implemented")
}
func (UnimplementedAppointmentServiceServer) ListCustomerPackages(context.Context, *ListCustomerPackagesRequest) (*ListCustomerPackagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCustomerPackages not implemented")
}
func (UnimplementedAppointmentServiceServer) mustEmbedUnimplementedAppointmentServiceServer() {}
func (UnimplementedAppointmentServiceServer) testEmbeddedByValue()                            {}

// UnsafeAppointmentServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AppointmentServiceServer will
// result in compilation errors.
type UnsafeAppointmentServiceServer interface {
	mustEmbedUnimplementedAppointmentServiceServer()
}

func RegisterAppointmentServiceServer(s grpc.ServiceRegistrar, srv AppointmentServiceServer) {
	// If the following call pancis, it indicates UnimplementedAppointmentServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AppointmentService_ServiceDesc, srv)
}

func _AppointmentService_CreateAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAppointmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).CreateAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppointmentService_CreateAppointment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).CreateAppointment(ctx, req.(*CreateAppointmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_ListAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAppointmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).ListAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppointmentService_ListAppointment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).ListAppointment(ctx, req.(*ListAppointmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_UpdateAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAppointmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).UpdateAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppointmentService_UpdateAppointment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).UpdateAppointment(ctx, req.(*UpdateAppointmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_GetAppointmentByIDs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppointmentByIDsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).GetAppointmentByIDs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppointmentService_GetAppointmentByIDs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).GetAppointmentByIDs(ctx, req.(*GetAppointmentByIDsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_ListCustomerPackages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCustomerPackagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).ListCustomerPackages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppointmentService_ListCustomerPackages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).ListCustomerPackages(ctx, req.(*ListCustomerPackagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AppointmentService_ServiceDesc is the grpc.ServiceDesc for AppointmentService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AppointmentService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.fulfillment.v1.AppointmentService",
	HandlerType: (*AppointmentServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAppointment",
			Handler:    _AppointmentService_CreateAppointment_Handler,
		},
		{
			MethodName: "ListAppointment",
			Handler:    _AppointmentService_ListAppointment_Handler,
		},
		{
			MethodName: "UpdateAppointment",
			Handler:    _AppointmentService_UpdateAppointment_Handler,
		},
		{
			MethodName: "GetAppointmentByIDs",
			Handler:    _AppointmentService_GetAppointmentByIDs_Handler,
		},
		{
			MethodName: "ListCustomerPackages",
			Handler:    _AppointmentService_ListCustomerPackages_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/fulfillment/v1/appointment_service.proto",
}
