syntax = "proto3";

// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: 临时性结构，待调用方全部重构完成会下线 --)
package backend.proto.offering.inner;

import "google/protobuf/timestamp.proto";
import "google/type/latlng.proto";
import "backend/proto/offering/inner/grooming_service.proto";

//
service GroomingServiceService {
  //
  rpc GetApplicableServiceList(GetApplicableServiceListRequest) returns (GetApplicableServiceListResponse);

  //
//  rpc GetApplicableEvaluationList(GetApplicableEvaluationListRequest) returns (GetApplicableEvaluationListResponse);
//
//  //
//  rpc GetServiceListByIds(GetServiceListByIdsRequest) returns (GetServiceListByIdsResponse);
//
//  //
//  rpc CustomizedServiceByPet(CustomizedServiceByPetRequest) returns (CustomizedServiceByPetResponse);
}

// get applicable service list request
message GetApplicableServiceListRequest {
  // company id
  int64 company_id = 1;
  // grooming_service 中的 service item type
  optional offering.inner.ServiceItemType service_item_type = 2;
  // business id, empty for all business in company
  optional int64 business_id = 3;
  // only return available services
  bool only_available = 4;
  // pet id
  optional int64 pet_id = 5;
  // service type
  offering.inner.ServiceType service_type = 6;
  // pet info, for service filter
  optional offering.inner.ServiceApplicableFilter filter = 7;
  // keyword, search by name
  optional string keyword = 8;
  // inactive
  optional bool inactive = 10;
  // customer address zipcode
  optional string zipcode = 11;
  // customer address coordinate
  optional google.type.LatLng coordinate = 12;
}

// get applicable service list response
message GetApplicableServiceListResponse {
  // service list
  repeated offering.inner.ServiceModel service_list = 1;
}

