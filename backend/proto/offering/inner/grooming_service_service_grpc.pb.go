// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/offering/inner/grooming_service_service.proto

// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: 临时性结构，待调用方全部重构完成会下线 --)

package offeringinnerpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	GroomingServiceService_GetApplicableServiceList_FullMethodName = "/backend.proto.offering.inner.GroomingServiceService/GetApplicableServiceList"
)

// GroomingServiceServiceClient is the client API for GroomingServiceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GroomingServiceServiceClient interface {
	GetApplicableServiceList(ctx context.Context, in *GetApplicableServiceListRequest, opts ...grpc.CallOption) (*GetApplicableServiceListResponse, error)
}

type groomingServiceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewGroomingServiceServiceClient(cc grpc.ClientConnInterface) GroomingServiceServiceClient {
	return &groomingServiceServiceClient{cc}
}

func (c *groomingServiceServiceClient) GetApplicableServiceList(ctx context.Context, in *GetApplicableServiceListRequest, opts ...grpc.CallOption) (*GetApplicableServiceListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetApplicableServiceListResponse)
	err := c.cc.Invoke(ctx, GroomingServiceService_GetApplicableServiceList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GroomingServiceServiceServer is the server API for GroomingServiceService service.
// All implementations must embed UnimplementedGroomingServiceServiceServer
// for forward compatibility.
type GroomingServiceServiceServer interface {
	GetApplicableServiceList(context.Context, *GetApplicableServiceListRequest) (*GetApplicableServiceListResponse, error)
	mustEmbedUnimplementedGroomingServiceServiceServer()
}

// UnimplementedGroomingServiceServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedGroomingServiceServiceServer struct{}

func (UnimplementedGroomingServiceServiceServer) GetApplicableServiceList(context.Context, *GetApplicableServiceListRequest) (*GetApplicableServiceListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetApplicableServiceList not implemented")
}
func (UnimplementedGroomingServiceServiceServer) mustEmbedUnimplementedGroomingServiceServiceServer() {
}
func (UnimplementedGroomingServiceServiceServer) testEmbeddedByValue() {}

// UnsafeGroomingServiceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GroomingServiceServiceServer will
// result in compilation errors.
type UnsafeGroomingServiceServiceServer interface {
	mustEmbedUnimplementedGroomingServiceServiceServer()
}

func RegisterGroomingServiceServiceServer(s grpc.ServiceRegistrar, srv GroomingServiceServiceServer) {
	// If the following call pancis, it indicates UnimplementedGroomingServiceServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&GroomingServiceService_ServiceDesc, srv)
}

func _GroomingServiceService_GetApplicableServiceList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApplicableServiceListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroomingServiceServiceServer).GetApplicableServiceList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GroomingServiceService_GetApplicableServiceList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroomingServiceServiceServer).GetApplicableServiceList(ctx, req.(*GetApplicableServiceListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// GroomingServiceService_ServiceDesc is the grpc.ServiceDesc for GroomingServiceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GroomingServiceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.offering.inner.GroomingServiceService",
	HandlerType: (*GroomingServiceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetApplicableServiceList",
			Handler:    _GroomingServiceService_GetApplicableServiceList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/offering/inner/grooming_service_service.proto",
}
