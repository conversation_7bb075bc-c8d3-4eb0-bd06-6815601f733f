// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.10
// 	protoc        (unknown)
// source: backend/proto/offering/inner/grooming_service_service.proto

// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: 临时性结构，待调用方全部重构完成会下线 --)

package offeringinnerpb

import (
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get applicable service list request
type GetApplicableServiceListRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// grooming_service 中的 service item type
	ServiceItemType *ServiceItemType `protobuf:"varint,2,opt,name=service_item_type,json=serviceItemType,proto3,enum=backend.proto.offering.inner.ServiceItemType,oneof" json:"service_item_type,omitempty"`
	// business id, empty for all business in company
	BusinessId *int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// only return available services
	OnlyAvailable bool `protobuf:"varint,4,opt,name=only_available,json=onlyAvailable,proto3" json:"only_available,omitempty"`
	// pet id
	PetId *int64 `protobuf:"varint,5,opt,name=pet_id,json=petId,proto3,oneof" json:"pet_id,omitempty"`
	// service type
	ServiceType ServiceType `protobuf:"varint,6,opt,name=service_type,json=serviceType,proto3,enum=backend.proto.offering.inner.ServiceType" json:"service_type,omitempty"`
	// pet info, for service filter
	Filter *ServiceApplicableFilter `protobuf:"bytes,7,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	// keyword, search by name
	Keyword *string `protobuf:"bytes,8,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"`
	// inactive
	Inactive *bool `protobuf:"varint,10,opt,name=inactive,proto3,oneof" json:"inactive,omitempty"`
	// customer address zipcode
	Zipcode *string `protobuf:"bytes,11,opt,name=zipcode,proto3,oneof" json:"zipcode,omitempty"`
	// customer address coordinate
	Coordinate    *latlng.LatLng `protobuf:"bytes,12,opt,name=coordinate,proto3,oneof" json:"coordinate,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetApplicableServiceListRequest) Reset() {
	*x = GetApplicableServiceListRequest{}
	mi := &file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetApplicableServiceListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApplicableServiceListRequest) ProtoMessage() {}

func (x *GetApplicableServiceListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApplicableServiceListRequest.ProtoReflect.Descriptor instead.
func (*GetApplicableServiceListRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetApplicableServiceListRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetApplicableServiceListRequest) GetServiceItemType() ServiceItemType {
	if x != nil && x.ServiceItemType != nil {
		return *x.ServiceItemType
	}
	return ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED
}

func (x *GetApplicableServiceListRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *GetApplicableServiceListRequest) GetOnlyAvailable() bool {
	if x != nil {
		return x.OnlyAvailable
	}
	return false
}

func (x *GetApplicableServiceListRequest) GetPetId() int64 {
	if x != nil && x.PetId != nil {
		return *x.PetId
	}
	return 0
}

func (x *GetApplicableServiceListRequest) GetServiceType() ServiceType {
	if x != nil {
		return x.ServiceType
	}
	return ServiceType_SERVICE_TYPE_UNSPECIFIED
}

func (x *GetApplicableServiceListRequest) GetFilter() *ServiceApplicableFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *GetApplicableServiceListRequest) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

func (x *GetApplicableServiceListRequest) GetInactive() bool {
	if x != nil && x.Inactive != nil {
		return *x.Inactive
	}
	return false
}

func (x *GetApplicableServiceListRequest) GetZipcode() string {
	if x != nil && x.Zipcode != nil {
		return *x.Zipcode
	}
	return ""
}

func (x *GetApplicableServiceListRequest) GetCoordinate() *latlng.LatLng {
	if x != nil {
		return x.Coordinate
	}
	return nil
}

// get applicable service list response
type GetApplicableServiceListResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// service list
	ServiceList   []*ServiceModel `protobuf:"bytes,1,rep,name=service_list,json=serviceList,proto3" json:"service_list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetApplicableServiceListResponse) Reset() {
	*x = GetApplicableServiceListResponse{}
	mi := &file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetApplicableServiceListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApplicableServiceListResponse) ProtoMessage() {}

func (x *GetApplicableServiceListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApplicableServiceListResponse.ProtoReflect.Descriptor instead.
func (*GetApplicableServiceListResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_inner_grooming_service_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetApplicableServiceListResponse) GetServiceList() []*ServiceModel {
	if x != nil {
		return x.ServiceList
	}
	return nil
}

var File_backend_proto_offering_inner_grooming_service_service_proto protoreflect.FileDescriptor

const file_backend_proto_offering_inner_grooming_service_service_proto_rawDesc = "" +
	"\n" +
	";backend/proto/offering/inner/grooming_service_service.proto\x12\x1cbackend.proto.offering.inner\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x18google/type/latlng.proto\x1a3backend/proto/offering/inner/grooming_service.proto\"\xb4\x05\n" +
	"\x1fGetApplicableServiceListRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\x12^\n" +
	"\x11service_item_type\x18\x02 \x01(\x0e2-.backend.proto.offering.inner.ServiceItemTypeH\x00R\x0fserviceItemType\x88\x01\x01\x12$\n" +
	"\vbusiness_id\x18\x03 \x01(\x03H\x01R\n" +
	"businessId\x88\x01\x01\x12%\n" +
	"\x0eonly_available\x18\x04 \x01(\bR\ronlyAvailable\x12\x1a\n" +
	"\x06pet_id\x18\x05 \x01(\x03H\x02R\x05petId\x88\x01\x01\x12L\n" +
	"\fservice_type\x18\x06 \x01(\x0e2).backend.proto.offering.inner.ServiceTypeR\vserviceType\x12R\n" +
	"\x06filter\x18\a \x01(\v25.backend.proto.offering.inner.ServiceApplicableFilterH\x03R\x06filter\x88\x01\x01\x12\x1d\n" +
	"\akeyword\x18\b \x01(\tH\x04R\akeyword\x88\x01\x01\x12\x1f\n" +
	"\binactive\x18\n" +
	" \x01(\bH\x05R\binactive\x88\x01\x01\x12\x1d\n" +
	"\azipcode\x18\v \x01(\tH\x06R\azipcode\x88\x01\x01\x128\n" +
	"\n" +
	"coordinate\x18\f \x01(\v2\x13.google.type.LatLngH\aR\n" +
	"coordinate\x88\x01\x01B\x14\n" +
	"\x12_service_item_typeB\x0e\n" +
	"\f_business_idB\t\n" +
	"\a_pet_idB\t\n" +
	"\a_filterB\n" +
	"\n" +
	"\b_keywordB\v\n" +
	"\t_inactiveB\n" +
	"\n" +
	"\b_zipcodeB\r\n" +
	"\v_coordinate\"q\n" +
	" GetApplicableServiceListResponse\x12M\n" +
	"\fservice_list\x18\x01 \x03(\v2*.backend.proto.offering.inner.ServiceModelR\vserviceList2\xb4\x01\n" +
	"\x16GroomingServiceService\x12\x99\x01\n" +
	"\x18GetApplicableServiceList\x12=.backend.proto.offering.inner.GetApplicableServiceListRequest\x1a>.backend.proto.offering.inner.GetApplicableServiceListResponseBv\n" +
	"&com.moego.backend.proto.offering.innerP\x01ZJgithub.com/MoeGolibrary/moego/backend/proto/offering/inner;offeringinnerpbb\x06proto3"

var (
	file_backend_proto_offering_inner_grooming_service_service_proto_rawDescOnce sync.Once
	file_backend_proto_offering_inner_grooming_service_service_proto_rawDescData []byte
)

func file_backend_proto_offering_inner_grooming_service_service_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_inner_grooming_service_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_inner_grooming_service_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_inner_grooming_service_service_proto_rawDesc), len(file_backend_proto_offering_inner_grooming_service_service_proto_rawDesc)))
	})
	return file_backend_proto_offering_inner_grooming_service_service_proto_rawDescData
}

var file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_backend_proto_offering_inner_grooming_service_service_proto_goTypes = []any{
	(*GetApplicableServiceListRequest)(nil),  // 0: backend.proto.offering.inner.GetApplicableServiceListRequest
	(*GetApplicableServiceListResponse)(nil), // 1: backend.proto.offering.inner.GetApplicableServiceListResponse
	(ServiceItemType)(0),                     // 2: backend.proto.offering.inner.ServiceItemType
	(ServiceType)(0),                         // 3: backend.proto.offering.inner.ServiceType
	(*ServiceApplicableFilter)(nil),          // 4: backend.proto.offering.inner.ServiceApplicableFilter
	(*latlng.LatLng)(nil),                    // 5: google.type.LatLng
	(*ServiceModel)(nil),                     // 6: backend.proto.offering.inner.ServiceModel
}
var file_backend_proto_offering_inner_grooming_service_service_proto_depIdxs = []int32{
	2, // 0: backend.proto.offering.inner.GetApplicableServiceListRequest.service_item_type:type_name -> backend.proto.offering.inner.ServiceItemType
	3, // 1: backend.proto.offering.inner.GetApplicableServiceListRequest.service_type:type_name -> backend.proto.offering.inner.ServiceType
	4, // 2: backend.proto.offering.inner.GetApplicableServiceListRequest.filter:type_name -> backend.proto.offering.inner.ServiceApplicableFilter
	5, // 3: backend.proto.offering.inner.GetApplicableServiceListRequest.coordinate:type_name -> google.type.LatLng
	6, // 4: backend.proto.offering.inner.GetApplicableServiceListResponse.service_list:type_name -> backend.proto.offering.inner.ServiceModel
	0, // 5: backend.proto.offering.inner.GroomingServiceService.GetApplicableServiceList:input_type -> backend.proto.offering.inner.GetApplicableServiceListRequest
	1, // 6: backend.proto.offering.inner.GroomingServiceService.GetApplicableServiceList:output_type -> backend.proto.offering.inner.GetApplicableServiceListResponse
	6, // [6:7] is the sub-list for method output_type
	5, // [5:6] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_inner_grooming_service_service_proto_init() }
func file_backend_proto_offering_inner_grooming_service_service_proto_init() {
	if File_backend_proto_offering_inner_grooming_service_service_proto != nil {
		return
	}
	file_backend_proto_offering_inner_grooming_service_proto_init()
	file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_inner_grooming_service_service_proto_rawDesc), len(file_backend_proto_offering_inner_grooming_service_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_offering_inner_grooming_service_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_inner_grooming_service_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_offering_inner_grooming_service_service_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_inner_grooming_service_service_proto = out.File
	file_backend_proto_offering_inner_grooming_service_service_proto_goTypes = nil
	file_backend_proto_offering_inner_grooming_service_service_proto_depIdxs = nil
}
