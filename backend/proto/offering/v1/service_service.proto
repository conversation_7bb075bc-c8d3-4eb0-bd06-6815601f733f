syntax = "proto3";

package backend.proto.offering.v1;

import "backend/proto/offering/v1/common.proto";
import "backend/proto/offering/v1/service.proto";
import "backend/proto/offering/v1/service_ob_setting.proto";
import "backend/proto/organization/v1/organization.proto";
import "buf/validate/validate.proto";
import "google/type/money.proto";
import "backend/proto/offering/v1/service_category.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.v1";

// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// (-- api-linter: core::0133::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// (-- api-linter: core::0134::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// (-- api-linter: core::0135::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// Service for managing Service resources.
service ServiceService {
  // 创建一个服务
  rpc CreateService(CreateServiceRequest) returns (CreateServiceResponse);

  // 获取服务
  rpc GetService(GetServiceRequest) returns (GetServiceResponse);

  // 更新服务
  rpc UpdateService(UpdateServiceRequest) returns (UpdateServiceResponse);

  // 删除服务
  rpc DeleteService(DeleteServiceRequest) returns (DeleteServiceResponse);

  // list service setting for company or business
  rpc ListServices(ListServicesRequest) returns (ListServicesResponse);

  // get ob services
  rpc ListOBServices(ListOBServicesRequest) returns (ListOBServicesResponse);

  // 更新 ob service
  rpc UpdateOBService(UpdateOBServiceRequest) returns (UpdateOBServiceResponse);

  // ListAvailableServices 全场景查询可用服务列表
  // 
  // 1. Pet Availability 过滤：
  //    - 当请求中包含 pet id 时，系统会根据以下 pet 配置信息过滤服务：
  //      * type/breed（宠物类型/品种，如: Dog/Golden Doodle, Cat/American Shorthair）  
  //      * code（宠物编码，商家自定义的标识）
  //      * weight/size（体型，如：small, medium, large, giant）
  //      * coat type（毛发类型，如：short, medium, long, double coat）
  //
  // 2. Staff Availability 过滤：
  //    - 当请求中包含 staff id 时，系统会根据 Service 的 available_staff 配置过滤服务
  //
  // 3. Lodging Availability 过滤：
  //    - 当请求中包含 lodging_unit_id 时，系统会根据 Service 的 available_lodging_type 配置过滤服务
  //
  // 4. Business Scope 过滤：
  //    - 系统会根据 Service 的 available_business 配置过滤服务范围
  //
  // 5. Additional Service 查询：
  //    - 当请求中包含 Service ID 时，系统会查询这些服务的 additional service 配置
  rpc ListAvailableServices(ListAvailableServicesRequest) returns (ListAvailableServicesResponse);

  // BatchUpdateServices 批量更新服务信息
  //
  // 支持批量更新服务的名称、排序值等基本信息
  rpc BatchUpdateServices(BatchUpdateServicesRequest) returns (BatchUpdateServicesResponse);


  // 批量获取服务
  rpc BatchGetServices(BatchGetServicesRequest) returns (BatchGetServicesResponse);

  // 获取 pet overrides
  rpc ListPetOverride(ListPetOverrideRequest) returns (ListPetOverrideResponse);

  // 创建/更新 pet override
  rpc UpsertPetOverride(UpsertPetOverrideRequest) returns (UpsertPetOverrideResponse);

  // 更新 pet override
  rpc UpdatePetOverride(UpdatePetOverrideRequest) returns (UpdatePetOverrideResponse);

  // 删除 pet override
  rpc DeletePetOverride(DeletePetOverrideRequest) returns (DeletePetOverrideResponse);

  // list service categories
  rpc ListServiceCategories(ListServiceCategoriesRequest) returns (ListServiceCategoriesResponse);

  // save service categories
  rpc SaveServiceCategories(SaveServiceCategoriesRequest) returns (SaveServiceCategoriesResponse);
}

// 删除服务请求
message DeleteServiceRequest {
  // 服务ID
  int64 service_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
}

// 删除服务响应
message DeleteServiceResponse {}

// 更新服务请求
message UpdateServiceRequest {
  // 服务更新配置
  ServiceUpdateDef service = 1 [(buf.validate.field) = {
    required: true
  }];
}

// 更新服务响应
message UpdateServiceResponse {}

// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: 不复用 model 结构，单独定义 ServiceCreateDef 结构 --)
// 创建服务请求
message CreateServiceRequest {
  // 服务创建配置
  ServiceCreateDef service = 1 [(buf.validate.field) = {
    required: true
  }];
}

// 创建服务响应
message CreateServiceResponse {
  // 服务 ID
  int64 service_id = 1;
}

// 获取服务请求
message GetServiceRequest {
  // 服务ID
  int64 service_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
}

// 获取服务响应
message GetServiceResponse {
  // 服务
  Service service = 1;
}

// 分类服务
message CategoryService {
  // 分类ID
  int64 category_id = 1;
  // 分类名称
  string name = 2;
  // 服务模板列表
  repeated Service services = 3;
}

// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 使用 PaginationRef 替代 --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 使用 PaginationRef 替代 --)
// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: 无 parent 语义 --)
// list ob services request
message ListOBServicesRequest{
  // The business_id
  int64 business_id = 1 [(buf.validate.field) = {
    required: true,
    int64: {gt: 0}
  }];
  // filter
  Filter filter = 2;
  // list filter
  message Filter {
    // service ids
    repeated int64 service_ids = 1 [(buf.validate.field) = {
      repeated: {
        min_items: 0
        max_items: 1000
      }
    }];
  }
  // 分页信息
  PaginationRef pagination = 9;
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 使用 PaginationRef 替代 --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 额外返回 total. --)
// list ob services response
message ListOBServicesResponse{
  // ob setting
  repeated ServiceOBSetting service_ob_settings = 1;
  // 分页信息
  optional PaginationRef pagination = 2;
  // 总数
  int32 total = 3;
}

// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 使用 PaginationRef 替代 --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 使用 PaginationRef 替代 --)
// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: 无 parent 语义 --)
// list services request
message ListServicesRequest {
  // The organization type
  backend.proto.organization.v1.OrganizationType organization_type = 1 [(buf.validate.field) = {
    required: true,
    enum: {
      defined_only: true
      not_in: [0]
    }
  }];

  // The organization ID.
  int64 organization_id = 2 [(buf.validate.field) = {
    required: true,
    int64: {gt: 0}
  }];
  // filter
  Filter filter = 3;

  // 分页信息
  PaginationRef pagination = 5;

  // list filter
  message Filter {
    // category ids
    repeated int64 category_ids = 1 [(buf.validate.field) = {
      repeated: {
        min_items: 0
        max_items: 100
      }
    }];

    // care type ids
    repeated int64 care_type_ids = 2 [(buf.validate.field) = {
      repeated: {
        min_items: 0
        max_items: 100
      }
    }];

    // statuses
    repeated Service.Status statuses = 3 [(buf.validate.field) = {
      repeated: {
        min_items: 0
        max_items: 10
      }
    }];
  }
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 使用 PaginationRef 替代 --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 额外返回 total. --)
// list setting services response
message ListServicesResponse {
  // 服务模板列表
  repeated Service services = 1;
  // 分页信息
  optional PaginationRef pagination = 2;
  // 总数
  int32 total = 3;
}

// (-- api-linter: core::0234::request-unknown-fields=disabled
//     aip.dev/not-precedent: 需要租户信息 --)
// (-- api-linter: core::0234::request-requests-field=disabled
//     aip.dev/not-precedent: 不复用 UpdateServiceRequest 结构 --)
// (-- api-linter: core::0234::request-parent-field=disabled
//     aip.dev/not-precedent: 无 parent 语义 --)
// 批量更新服务请求
message BatchUpdateServicesRequest {
  // 当前的租户类型
  backend.proto.organization.v1.OrganizationType organization_type = 1 [(buf.validate.field) = {
    enum: {
      defined_only: true
      not_in: [0]
    }
  }];

  // 当前的租户 ID
  int64 organization_id = 2 [(buf.validate.field) = {
    int64: {gt: 0}
  }];

  // 服务更新配置列表
  repeated ServiceUpdateDef update_services = 3 [(buf.validate.field) = {
    repeated: {
      min_items: 1
      max_items: 1000
    }
  }];
}

// 单个服务的更新配置
message ServiceUpdateDef {
  // 服务 ID
  int64 id = 1 [(buf.validate.field) = {
    int64: {gt: 0}
  }];

  // 分类 ID（可选）
  optional int64 category_id = 2 [(buf.validate.field) = {
    int64: {gt: 0}
  }];

  // 服务名称（可选）
  optional string name = 3 [(buf.validate.field) = {
    string: {max_len: 100}
  }];

  // 描述
  optional string description = 4 [(buf.validate.field) = {
    string: {max_len: 1000}
  }];

  // 颜色
  optional string color_code = 5 [(buf.validate.field) = {
    string: {max_len: 10}
  }];

  // 排序值（可选）
  optional int64 sort = 6 [(buf.validate.field) = {
    int64: {gt: 0}
  }];

  // 图片
  repeated string images = 7 [(buf.validate.field) = {
    repeated: {
      min_items: 0
      max_items: 1000
      unique: true
      items: {
        string: {max_len: 1024}
      }
    }
  }];

  // 状态
  optional Service.Status status = 8 [(buf.validate.field) = {
    enum: {
      defined_only: true
      not_in: [0]
    }
  }];

  // 可用 business scope
  optional AvailableBusiness available_business = 9;

  // 关联的属性值
  optional ServiceAttributes attributes = 10;

  // 附加服务配置（可选）
  optional AdditionalService additional_service = 11;

  // 可用宠物类型和品种配置（可选）
  optional AvailablePetTypeBreed available_type_breed = 12;

  // 可用宠物尺寸配置（可选）
  optional AvailablePetSize available_pet_size = 13;

  // 可用宠物毛类型配置（可选）
  optional AvailableCoatType available_coat_type = 14;

  // 可用宠物代码配置（可选）
  optional AvailablePetCode available_pet_code = 15;

  // 可用宠物体重配置（可选）
  optional AvailablePetWeight available_pet_weight = 16;

  // 价格配置
  optional google.type.Money price = 17;

  // 税收 ID
  optional int64 tax_id = 19;

  // Business and staff override configuration (price, duration, tax_id overrides for specific businesses and staff)
  repeated BusinessStaffOverride business_staff_overrides = 20;
}

// (-- api-linter: core::0234::response-resource-field=disabled
//     aip.dev/not-precedent: 不返回 Service 类型 --)
// 批量更新服务响应
message BatchUpdateServicesResponse {}

// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 使用 PaginationRef 替代 --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 使用 PaginationRef 替代 --)
// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: 无 parent 语义 --)
// 查询可用服务请求（用于预约场景）
message ListAvailableServicesRequest {
  // 当前的租户类型，如果是 Business 会包含 override 信息
  backend.proto.organization.v1.OrganizationType organization_type = 1 [(buf.validate.field) = {
    enum: {
      defined_only: true
      not_in: [0]
    }
  }];

  // 当前的租户 ID
  int64 organization_id = 2 [(buf.validate.field) = {
    int64: {gt: 0}
  }];

  // 包含的可用性上下文，比如选择 Business/Pet/Staff/Lodging/Service 等
  AvailabilityContext context = 3;

  // 可选的过滤条件
  optional Filter filter = 5;

  // 分页信息
  optional PaginationRef pagination = 6;

  // 可用性上下文控制选项。定义了动态查询的上下文，包括 Business/Staff/Lodging/Pet 等资源用于进行复杂的可用性过滤。
  message AvailabilityContext {
    // 当前选择的 Staff ID, 用于 Available staff 过滤。包含 Staff Override 信息
    optional int64 staff_id = 2 [(buf.validate.field) = {
      int64: {gt: 0}
    }];

    // 当前选择的 Lodging Unit ID，用于 Eligible lodging type 过滤
    optional int64 lodging_unit_id = 3 [(buf.validate.field) = {
      int64: {gt: 0}
    }];

    // 当前选择的 Pet ID，用于 Pet details 过滤，包括 Type&Breed/Weight/Pet Size/Pet Code/Coat Type，也包括 Pet Override 信息
    repeated int64 pet_ids = 4 [(buf.validate.field) = {
      repeated: {
        min_items: 0
        max_items: 100
        unique: true
        items: {
          int64: {gt: 0}
        }
      }
    }];

    // 当前选择的 Service ID, 查询的是该主服务下的“附加服务(Additional Service)”列表。
    repeated int64 service_ids = 5 [(buf.validate.field) = {
      repeated: {
        min_items: 0
        max_items: 100
        unique: true
        items: {
          int64: {gt: 0}
        }
      }
    }];
  }

  // 过滤条件
  message Filter {
    // 护理类型
    repeated int64 care_type_ids = 1 [(buf.validate.field) = {
      repeated: {
        min_items: 0
        max_items: 100
        unique: true
        items: {
          int64: {gt: 0}
        }
      }
    }];

    // 分类
    repeated int64 category_ids = 2 [(buf.validate.field) = {
      repeated: {
        min_items: 0
        max_items: 100
        unique: true
        items: {
          int64: {gt: 0}
        }
      }
    }];

    // 状态（如：active/inactive 等）
    repeated Service.Status statuses = 3 [(buf.validate.field) = {
      repeated: {
        min_items: 0
        max_items: 10
        unique: true
        items: {
          enum: {
            defined_only: true
            not_in: [0]
          }
        }
      }
    }];

    // 服务来源
    repeated OfferingSource sources = 5 [(buf.validate.field) = {
      repeated: {
        min_items: 0
        max_items: 100
        unique: true
        items: {
          enum: {
            defined_only: true
            not_in: [0]
          }
        }
      }
    }];

    // 关键词搜索（如服务名称等）
    optional string keyword = 4 [(buf.validate.field) = {
      string: {max_len: 100}
    }];
  }
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 使用 PaginationRef 替代 --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 额外返回 total. --)
// 查询可用服务响应
message ListAvailableServicesResponse {
  // 可用服务列表
  repeated AvailableService services = 1;

  // 分页信息
  optional PaginationRef pagination = 2;

  // 总数
  int32 total = 3;

  // assocaited models

}

// AvailableService 用于 ListAvailableServices 的返回结构
// 只包含前端消费所需的信息，过滤掉纯后端配置信息
message AvailableService {
  // 主键 ID
  int64 id = 1;
  // 护理类型 ID
  int64 care_type_id = 2;
  // 分类 ID
  int64 category_id = 3;
  // 服务名称
  string name = 4;
  // 服务描述
  string description = 5;
  // 服务颜色
  string color_code = 6;
  // 排序值
  int64 sort = 7;
  // 图片
  repeated string images = 8;
  // 来源
  OfferingSource source = 9;
  // 状态
  Service.Status status = 10;
  // 价格
  google.type.Money price = 11;
  // 税收 ID
  int64 tax_id = 12;
  // 服务属性
  optional AvailableServiceAttributes attributes = 13;
  
  // 企业员工覆盖信息
  repeated BusinessStaffOverride business_staff_overrides = 14;
  // 宠物覆盖信息
  repeated PetOverride pet_overrides = 15;

}

// 可用服务属性（只包含前端需要的，大部分是静态显示内容，少部分是前端消费的配置）
message AvailableServiceAttributes {
  // 服务时长，常见于 GROOMING, DOG_WALKING 服务
  optional int32 duration = 1;
  
  // 最大服务时长，常见于 DAYCARE 服务
  optional int32 max_duration = 2;
  
  // 是否需要员工，常见于 ADD_ON 类型服务
  optional bool is_required_staff = 3;
  
  // 在线预约别名，常见于 EVALUATION 服务
  optional string ob_alias = 4;
  
  // 价格单位，常见于 BOARDING 服务
  optional PriceUnit price_unit = 5;
}

//UpdateOBServiceRequest
message UpdateOBServiceRequest{
  // The business_id
  int64 business_id = 2;
  // 服务
  int64 service_id = 3 [(buf.validate.field) = { int64: { gt: 0 } }];
  // Whether the service is available for online booking
  optional bool is_available = 11;
  // Display price mode: 0 - Do not show, 1 - Show fixed price, 2 - Show "starting at", 3 - Show "varies"
  optional ServiceOBSetting.ShowBasePriceMode show_base_price = 12;
  // Whether all staff are available for this service when booking online
  optional bool is_all_staff = 13;
  //staff ids
  repeated int64 staff_ids = 14;
  // Display duration mode: 0 - Do not show, 1 - Show duration
  optional ServiceOBSetting.ShowDuration show_duration = 15;
}

//Update OB Service Response
message UpdateOBServiceResponse{
}

// (-- api-linter: core::0231::request-unknown-fields=disabled
//     aip.dev/not-precedent: 直接根据 ID 批量获取服务 --)
// (-- api-linter: core::0231::request-names-field=disabled
//     aip.dev/not-precedent: 直接根据 ID 批量获取服务 --)
// 批量获取服务请求
message BatchGetServicesRequest {
  // 当前的租户类型
  backend.proto.organization.v1.OrganizationType organization_type = 1 [(buf.validate.field) = {
    enum: {
      defined_only: true
      not_in: [0]
    }
  }];

  // 当前的租户 ID
  int64 organization_id = 2 [(buf.validate.field) = {
    int64: {gt: 0}
  }];

  // 服务ID列表
  repeated int64 ids = 3 [(buf.validate.field) = {
    repeated: {
      min_items: 1
      max_items: 100
      unique: true
      items: {
        int64: {gt: 0}
      }
    }
  }];
}

// 批量获取服务响应
message BatchGetServicesResponse {
  // 服务列表
  repeated Service services = 1;
}

// Service 创建配置定义
message ServiceCreateDef {
  // 组织类型
  backend.proto.organization.v1.OrganizationType organization_type = 1 [(buf.validate.field) = {
    required: true,
    enum: {
      defined_only: true
      not_in: [0]
    }
  }];

  // 组织 ID
  int64 organization_id = 2 [(buf.validate.field) = {
    required: true,
    int64: {gt: 0}
  }];

  // 护理类型 ID
  int64 care_type_id = 3 [(buf.validate.field) = {
    required: true,
    int64: {gt: 0}
  }];

  // 分类 ID（可选）
  optional int64 category_id = 4 [(buf.validate.field) = {
    int64: {gt: 0}
  }];

  // 服务名称
  string name = 5 [(buf.validate.field) = {
    required: true,
    string: {
      min_len: 1,
      max_len: 100
    }
  }];

  // 描述（可选）
  optional string description = 6 [(buf.validate.field) = {
    string: {max_len: 1000}
  }];

  // 颜色代码
  string color_code = 7 [(buf.validate.field) = {
    required: true,
    string: {
      min_len: 1,
      max_len: 10
    }
  }];

  // 图片列表（可选）
  repeated string images = 9 [(buf.validate.field) = {
    repeated: {
      min_items: 0,
      max_items: 1000,
      unique: true,
      items: {
        string: {max_len: 1024}
      }
    }
  }];

  // 来源
  OfferingSource source = 10 [(buf.validate.field) = {
    required: true,
    enum: {
      defined_only: true
      not_in: [0]
    }
  }];

  // 状态
  Service.Status status = 11 [(buf.validate.field) = {
    required: true,
    enum: {
      defined_only: true
      not_in: [0]
    }
  }];

  // 可用业务范围
  AvailableBusiness available_business = 13 [(buf.validate.field) = {
    required: true
  }];

  // 附加服务配置（可选）
  optional AdditionalService additional_service = 14;

  // 可用宠物类型和品种配置（可选）
  optional AvailablePetTypeBreed available_type_breed = 15;

  // 可用宠物尺寸配置（可选）
  optional AvailablePetSize available_pet_size = 16;

  // 可用宠物毛类型配置（可选）
  optional AvailableCoatType available_coat_type = 17;

  // 可用宠物代码配置（可选）
  optional AvailablePetCode available_pet_code = 18;

  // 服务属性（可选）
  optional ServiceAttributes attributes = 19;

  // 可用宠物体重配置（可选）
  optional AvailablePetWeight available_pet_weight = 20;

  // 价格配置
  google.type.Money price = 21 [(buf.validate.field) = {
    required: true
  }];

  // 税收 ID
  int64 tax_id = 23 [(buf.validate.field) = {
    int64: {gt: 0}
  }];

  // Business and staff override configuration (price, duration, tax_id overrides for specific businesses and staff)
  repeated BusinessStaffOverride business_staff_overrides = 25;
}

// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 使用 PaginationRef 替代 --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 使用 PaginationRef 替代 --)
// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: 无 parent 语义 --)
// ListPetOverridesRequest
message ListPetOverrideRequest {
  // pet id
  repeated int64 pet_ids = 1 [(buf.validate.field) = {
    repeated: {
      min_items: 1
      max_items: 100
      unique: true
      items: {
        int64: {gt: 0}
      }
    }
  }];
  // 分页信息
  PaginationRef pagination = 2;
}

// ListPetOverridesResponse
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 使用 PaginationRef 替代 --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 额外返回 total. --)
message ListPetOverrideResponse {
  // 服务宠物覆盖列表
  repeated PetOverride pet_overrides = 1;
  // 分页信息
  PaginationRef pagination = 2;
  // 总数
  int32 total = 3;
}

// UpsertPetOverrideRequest
// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: 不复用 model 结构，单独定义 PetOverrideDef 结构 --)
// 创建服务请求
message UpsertPetOverrideRequest {
  // pet override
  PetOverrideDef pet_override = 1  [(buf.validate.field) = {
    required: true
  }];
}

// UpsertPetOverrideResponse
message UpsertPetOverrideResponse {
  // 服务宠物覆盖列表
  PetOverride pet_override = 1;
}

// UpdatePetOverrideRequest
message UpdatePetOverrideRequest {
  // pet override id
  int64 id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
  // pet override
  PetOverrideDef pet_override = 2  [(buf.validate.field) = {
    required: true
  }];
}

// UpdatePetOverrideResponse
message UpdatePetOverrideResponse {
  // 服务宠物覆盖列表
  PetOverride pet_override = 1;
}

// DeletePetOverrideRequest
message DeletePetOverrideRequest {
  // pet override id
  int64 id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
}

// DeletePetOverrideResponse
message DeletePetOverrideResponse {
}

// PetOverrideDef
message PetOverrideDef {
  // pet id
  int64 pet_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
  // service id
  int64 service_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
  // price
  optional google.type.Money price = 3;
  // duration
  optional int32 duration = 4;
}

// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: no need page --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: no need page --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: no need page --)
// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: 无 parent 语义 --)
// list service category request
message ListServiceCategoriesRequest{
  // The organization type
  backend.proto.organization.v1.OrganizationType organization_type = 1;
  // The organization ID.
  int64 organization_id = 2;
  // filter
  Filter filter = 18;

  // list filter
  message Filter {
    // category ids
    repeated int64 category_ids = 2;
    // care type ids
    repeated int64 care_type_ids = 3;
  }
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: no need page --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: no need page --)
// list service setting services response
message ListServiceCategoriesResponse{
  // category list
  repeated ServiceCategory categories = 1;
}

// save categories request
message SaveServiceCategoriesRequest{
  // The type of the organization (e.g., company, enterprise).
  backend.proto.organization.v1.OrganizationType organization_type = 1;
  // The ID of the organization this category belongs to.
  int64 organization_id = 2;
  // create category list
  repeated CategoryCreateDef create_categories = 3 ;
  // update category list
  repeated CategoryUpdateDef update_categories = 4 ;
  // delete ids category
  repeated int64 delete_ids = 5 ;


  // Defines the structure for a service category, used to organize services.
  message CategoryUpdateDef {
    // Primary key ID of the service category.
    int64 id = 1;
    // The ID of the care type associated with this category.
    int64 care_type_id = 2 ;
    // The name of the service category, unique within the same organization and care type.
    string name = 3 ;
    // sort order of the category
    int64 sort = 4 ;
  }

  // Defines the structure for a service category, used to organize services.
  message CategoryCreateDef {
    // The ID of the care type associated with this category.
    int64 care_type_id = 2 ;
    // The name of the service category, unique within the same organization and care type.
    string name = 3 ;
    // sort order of the category
    int64 sort = 4 ;
  }
}
// save categories response
message SaveServiceCategoriesResponse{
}
