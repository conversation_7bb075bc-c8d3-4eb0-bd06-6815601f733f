// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.10
// 	protoc        (unknown)
// source: backend/proto/offering/v1/service.proto

package offeringpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	v1 "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Override type
type OverrideType int32

const (
	// Unspecified
	OverrideType_OVERRIDE_TYPE_UNSPECIFIED OverrideType = 0
	// Business
	OverrideType_BUSINESS OverrideType = 1
	// Pet
	OverrideType_PET OverrideType = 2
	// Staff
	OverrideType_STAFF OverrideType = 3
	// Zone
	OverrideType_ZONE OverrideType = 4
)

// Enum value maps for OverrideType.
var (
	OverrideType_name = map[int32]string{
		0: "OVERRIDE_TYPE_UNSPECIFIED",
		1: "BUSINESS",
		2: "PET",
		3: "STAFF",
		4: "ZONE",
	}
	OverrideType_value = map[string]int32{
		"OVERRIDE_TYPE_UNSPECIFIED": 0,
		"BUSINESS":                  1,
		"PET":                       2,
		"STAFF":                     3,
		"ZONE":                      4,
	}
)

func (x OverrideType) Enum() *OverrideType {
	p := new(OverrideType)
	*p = x
	return p
}

func (x OverrideType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OverrideType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_service_proto_enumTypes[0].Descriptor()
}

func (OverrideType) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_service_proto_enumTypes[0]
}

func (x OverrideType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OverrideType.Descriptor instead.
func (OverrideType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{0}
}

// Pet availability scope
type PetAvailabilityScopeType int32

const (
	// Unspecified
	PetAvailabilityScopeType_PET_AVAILABILITY_SCOPE_TYPE_UNSPECIFIED PetAvailabilityScopeType = 0
	// All pet types and breeds
	PetAvailabilityScopeType_ALL_TYPES_AND_BREEDS PetAvailabilityScopeType = 1
	// All pet breeds for specific pet type
	PetAvailabilityScopeType_SPECIFIC_TYPE PetAvailabilityScopeType = 2
	// Specific pet breed
	PetAvailabilityScopeType_SPECIFIC_BREED PetAvailabilityScopeType = 3
	// All pet sizes
	PetAvailabilityScopeType_ALL_SIZES PetAvailabilityScopeType = 11
	// Specific pet size
	PetAvailabilityScopeType_SPECIFIC_SIZE PetAvailabilityScopeType = 12
	// All pet coat types
	PetAvailabilityScopeType_ALL_COAT_TYPES PetAvailabilityScopeType = 21
	// Specific pet coat type
	PetAvailabilityScopeType_SPECIFIC_COAT_TYPE PetAvailabilityScopeType = 22
	// All pet codes
	PetAvailabilityScopeType_ALL_CODES PetAvailabilityScopeType = 31
	// Specific pet code
	PetAvailabilityScopeType_SPECIFIC_CODE PetAvailabilityScopeType = 32
	// Exclude all pet codes
	PetAvailabilityScopeType_EXCLUDE_ALL_CODES PetAvailabilityScopeType = 33
	// Exclude specific pet code
	PetAvailabilityScopeType_EXCLUDE_SPECIFIC_CODE PetAvailabilityScopeType = 34
)

// Enum value maps for PetAvailabilityScopeType.
var (
	PetAvailabilityScopeType_name = map[int32]string{
		0:  "PET_AVAILABILITY_SCOPE_TYPE_UNSPECIFIED",
		1:  "ALL_TYPES_AND_BREEDS",
		2:  "SPECIFIC_TYPE",
		3:  "SPECIFIC_BREED",
		11: "ALL_SIZES",
		12: "SPECIFIC_SIZE",
		21: "ALL_COAT_TYPES",
		22: "SPECIFIC_COAT_TYPE",
		31: "ALL_CODES",
		32: "SPECIFIC_CODE",
		33: "EXCLUDE_ALL_CODES",
		34: "EXCLUDE_SPECIFIC_CODE",
	}
	PetAvailabilityScopeType_value = map[string]int32{
		"PET_AVAILABILITY_SCOPE_TYPE_UNSPECIFIED": 0,
		"ALL_TYPES_AND_BREEDS":                    1,
		"SPECIFIC_TYPE":                           2,
		"SPECIFIC_BREED":                          3,
		"ALL_SIZES":                               11,
		"SPECIFIC_SIZE":                           12,
		"ALL_COAT_TYPES":                          21,
		"SPECIFIC_COAT_TYPE":                      22,
		"ALL_CODES":                               31,
		"SPECIFIC_CODE":                           32,
		"EXCLUDE_ALL_CODES":                       33,
		"EXCLUDE_SPECIFIC_CODE":                   34,
	}
)

func (x PetAvailabilityScopeType) Enum() *PetAvailabilityScopeType {
	p := new(PetAvailabilityScopeType)
	*p = x
	return p
}

func (x PetAvailabilityScopeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PetAvailabilityScopeType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_service_proto_enumTypes[1].Descriptor()
}

func (PetAvailabilityScopeType) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_service_proto_enumTypes[1]
}

func (x PetAvailabilityScopeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PetAvailabilityScopeType.Descriptor instead.
func (PetAvailabilityScopeType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{1}
}

// Pet availability rule type for availability configuration
type AvailabilityRuleType int32

const (
	// Unspecified
	AvailabilityRuleType_AVAILABILITY_RULE_TYPE_UNSPECIFIED AvailabilityRuleType = 0
	// No restriction
	AvailabilityRuleType_NO_RESTRICTION AvailabilityRuleType = 1
	// Include (whitelist) - only allow specified items
	AvailabilityRuleType_INCLUDE AvailabilityRuleType = 2
	// Exclude (blacklist) - exclude specified items
	AvailabilityRuleType_EXCLUDE AvailabilityRuleType = 3
)

// Enum value maps for AvailabilityRuleType.
var (
	AvailabilityRuleType_name = map[int32]string{
		0: "AVAILABILITY_RULE_TYPE_UNSPECIFIED",
		1: "NO_RESTRICTION",
		2: "INCLUDE",
		3: "EXCLUDE",
	}
	AvailabilityRuleType_value = map[string]int32{
		"AVAILABILITY_RULE_TYPE_UNSPECIFIED": 0,
		"NO_RESTRICTION":                     1,
		"INCLUDE":                            2,
		"EXCLUDE":                            3,
	}
)

func (x AvailabilityRuleType) Enum() *AvailabilityRuleType {
	p := new(AvailabilityRuleType)
	*p = x
	return p
}

func (x AvailabilityRuleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AvailabilityRuleType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_service_proto_enumTypes[2].Descriptor()
}

func (AvailabilityRuleType) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_service_proto_enumTypes[2]
}

func (x AvailabilityRuleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AvailabilityRuleType.Descriptor instead.
func (AvailabilityRuleType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{2}
}

// Price unit.
type PriceUnit int32

const (
	// Unspecified.
	PriceUnit_PRICE_UNIT_UNSPECIFIED PriceUnit = 0
	// Per session
	PriceUnit_PER_SESSION PriceUnit = 1
	// Per night
	PriceUnit_PER_NIGHT PriceUnit = 2
	// Per hour
	PriceUnit_PER_HOUR PriceUnit = 3
	// Per day
	PriceUnit_PER_DAY PriceUnit = 4
)

// Enum value maps for PriceUnit.
var (
	PriceUnit_name = map[int32]string{
		0: "PRICE_UNIT_UNSPECIFIED",
		1: "PER_SESSION",
		2: "PER_NIGHT",
		3: "PER_HOUR",
		4: "PER_DAY",
	}
	PriceUnit_value = map[string]int32{
		"PRICE_UNIT_UNSPECIFIED": 0,
		"PER_SESSION":            1,
		"PER_NIGHT":              2,
		"PER_HOUR":               3,
		"PER_DAY":                4,
	}
)

func (x PriceUnit) Enum() *PriceUnit {
	p := new(PriceUnit)
	*p = x
	return p
}

func (x PriceUnit) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PriceUnit) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_service_proto_enumTypes[3].Descriptor()
}

func (PriceUnit) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_service_proto_enumTypes[3]
}

func (x PriceUnit) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PriceUnit.Descriptor instead.
func (PriceUnit) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{3}
}

// (-- api-linter: core::0216::synonyms=disabled
//
//	aip.dev/not-precedent: 保持 status 命名设计. --)
//
// The status of the service.
type Service_Status int32

const (
	// Unspecified
	Service_STATUS_UNSPECIFIED Service_Status = 0
	// Active
	Service_ACTIVE Service_Status = 1
	// Inactive
	Service_INACTIVE Service_Status = 2
)

// Enum value maps for Service_Status.
var (
	Service_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "ACTIVE",
		2: "INACTIVE",
	}
	Service_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"ACTIVE":             1,
		"INACTIVE":           2,
	}
)

func (x Service_Status) Enum() *Service_Status {
	p := new(Service_Status)
	*p = x
	return p
}

func (x Service_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Service_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_service_proto_enumTypes[4].Descriptor()
}

func (Service_Status) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_service_proto_enumTypes[4]
}

func (x Service_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Service_Status.Descriptor instead.
func (Service_Status) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{0, 0}
}

// Service type.
type Service_Type int32

const (
	// Unspecified.
	Service_TYPE_UNSPECIFIED Service_Type = 0
	// Service.
	Service_SERVICE Service_Type = 1
	// Add-On.
	Service_ADD_ON Service_Type = 2
)

// Enum value maps for Service_Type.
var (
	Service_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "SERVICE",
		2: "ADD_ON",
	}
	Service_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"SERVICE":          1,
		"ADD_ON":           2,
	}
)

func (x Service_Type) Enum() *Service_Type {
	p := new(Service_Type)
	*p = x
	return p
}

func (x Service_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Service_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_service_proto_enumTypes[5].Descriptor()
}

func (Service_Type) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_service_proto_enumTypes[5]
}

func (x Service_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Service_Type.Descriptor instead.
func (Service_Type) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{0, 1}
}

// Result validity period type
type ResultValidityPeriod_ValidityPeriodType int32

const (
	// Unspecified
	ResultValidityPeriod_VALIDITY_PERIOD_TYPE_UNSPECIFIED ResultValidityPeriod_ValidityPeriodType = 0
	// Always valid - results never expire
	ResultValidityPeriod_ALWAYS_VALID ResultValidityPeriod_ValidityPeriodType = 1
	// Conditional validity - clear 'Pass' status if the pet hasn't had relevant services for a specified period
	ResultValidityPeriod_CONDITIONAL ResultValidityPeriod_ValidityPeriodType = 2
)

// Enum value maps for ResultValidityPeriod_ValidityPeriodType.
var (
	ResultValidityPeriod_ValidityPeriodType_name = map[int32]string{
		0: "VALIDITY_PERIOD_TYPE_UNSPECIFIED",
		1: "ALWAYS_VALID",
		2: "CONDITIONAL",
	}
	ResultValidityPeriod_ValidityPeriodType_value = map[string]int32{
		"VALIDITY_PERIOD_TYPE_UNSPECIFIED": 0,
		"ALWAYS_VALID":                     1,
		"CONDITIONAL":                      2,
	}
)

func (x ResultValidityPeriod_ValidityPeriodType) Enum() *ResultValidityPeriod_ValidityPeriodType {
	p := new(ResultValidityPeriod_ValidityPeriodType)
	*p = x
	return p
}

func (x ResultValidityPeriod_ValidityPeriodType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ResultValidityPeriod_ValidityPeriodType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_service_proto_enumTypes[6].Descriptor()
}

func (ResultValidityPeriod_ValidityPeriodType) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_service_proto_enumTypes[6]
}

func (x ResultValidityPeriod_ValidityPeriodType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ResultValidityPeriod_ValidityPeriodType.Descriptor instead.
func (ResultValidityPeriod_ValidityPeriodType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{8, 0}
}

// Duration unit for validity period
type ValidityDuration_DurationUnit int32

const (
	// Unspecified
	ValidityDuration_DURATION_UNIT_UNSPECIFIED ValidityDuration_DurationUnit = 0
	// Days
	ValidityDuration_DAYS ValidityDuration_DurationUnit = 1
	// Weeks
	ValidityDuration_WEEKS ValidityDuration_DurationUnit = 2
	// Months
	ValidityDuration_MONTHS ValidityDuration_DurationUnit = 3
	// Years
	ValidityDuration_YEARS ValidityDuration_DurationUnit = 4
)

// Enum value maps for ValidityDuration_DurationUnit.
var (
	ValidityDuration_DurationUnit_name = map[int32]string{
		0: "DURATION_UNIT_UNSPECIFIED",
		1: "DAYS",
		2: "WEEKS",
		3: "MONTHS",
		4: "YEARS",
	}
	ValidityDuration_DurationUnit_value = map[string]int32{
		"DURATION_UNIT_UNSPECIFIED": 0,
		"DAYS":                      1,
		"WEEKS":                     2,
		"MONTHS":                    3,
		"YEARS":                     4,
	}
)

func (x ValidityDuration_DurationUnit) Enum() *ValidityDuration_DurationUnit {
	p := new(ValidityDuration_DurationUnit)
	*p = x
	return p
}

func (x ValidityDuration_DurationUnit) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ValidityDuration_DurationUnit) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_service_proto_enumTypes[7].Descriptor()
}

func (ValidityDuration_DurationUnit) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_service_proto_enumTypes[7]
}

func (x ValidityDuration_DurationUnit) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ValidityDuration_DurationUnit.Descriptor instead.
func (ValidityDuration_DurationUnit) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{9, 0}
}

// Date type for default service
type ConditionalDefaultService_DateType int32

const (
	// Unspecified
	ConditionalDefaultService_DATE_TYPE_UNSPECIFIED ConditionalDefaultService_DateType = 0
	// Everyday except the checkout day
	ConditionalDefaultService_EVERYDAY_EXCEPT_CHECKOUT_DAY ConditionalDefaultService_DateType = 1
	// Everyday
	ConditionalDefaultService_EVERYDAY ConditionalDefaultService_DateType = 4
	// Everyday except the check-in day
	ConditionalDefaultService_EVERYDAY_EXCEPT_CHECKIN_DAY ConditionalDefaultService_DateType = 5
	// Last day
	ConditionalDefaultService_LAST_DAY ConditionalDefaultService_DateType = 6
	// First day
	ConditionalDefaultService_FIRST_DAY ConditionalDefaultService_DateType = 7
)

// Enum value maps for ConditionalDefaultService_DateType.
var (
	ConditionalDefaultService_DateType_name = map[int32]string{
		0: "DATE_TYPE_UNSPECIFIED",
		1: "EVERYDAY_EXCEPT_CHECKOUT_DAY",
		4: "EVERYDAY",
		5: "EVERYDAY_EXCEPT_CHECKIN_DAY",
		6: "LAST_DAY",
		7: "FIRST_DAY",
	}
	ConditionalDefaultService_DateType_value = map[string]int32{
		"DATE_TYPE_UNSPECIFIED":        0,
		"EVERYDAY_EXCEPT_CHECKOUT_DAY": 1,
		"EVERYDAY":                     4,
		"EVERYDAY_EXCEPT_CHECKIN_DAY":  5,
		"LAST_DAY":                     6,
		"FIRST_DAY":                    7,
	}
)

func (x ConditionalDefaultService_DateType) Enum() *ConditionalDefaultService_DateType {
	p := new(ConditionalDefaultService_DateType)
	*p = x
	return p
}

func (x ConditionalDefaultService_DateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConditionalDefaultService_DateType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_service_proto_enumTypes[8].Descriptor()
}

func (ConditionalDefaultService_DateType) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_service_proto_enumTypes[8]
}

func (x ConditionalDefaultService_DateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConditionalDefaultService_DateType.Descriptor instead.
func (ConditionalDefaultService_DateType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{18, 0}
}

// Stay length unit for default service rules
type ConditionalDefaultService_StayUnit int32

const (
	// Unspecified
	ConditionalDefaultService_STAY_UNIT_UNSPECIFIED ConditionalDefaultService_StayUnit = 0
	// Days
	ConditionalDefaultService_DAYS ConditionalDefaultService_StayUnit = 1
	// Nights
	ConditionalDefaultService_NIGHTS ConditionalDefaultService_StayUnit = 2
)

// Enum value maps for ConditionalDefaultService_StayUnit.
var (
	ConditionalDefaultService_StayUnit_name = map[int32]string{
		0: "STAY_UNIT_UNSPECIFIED",
		1: "DAYS",
		2: "NIGHTS",
	}
	ConditionalDefaultService_StayUnit_value = map[string]int32{
		"STAY_UNIT_UNSPECIFIED": 0,
		"DAYS":                  1,
		"NIGHTS":                2,
	}
)

func (x ConditionalDefaultService_StayUnit) Enum() *ConditionalDefaultService_StayUnit {
	p := new(ConditionalDefaultService_StayUnit)
	*p = x
	return p
}

func (x ConditionalDefaultService_StayUnit) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConditionalDefaultService_StayUnit) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_service_proto_enumTypes[9].Descriptor()
}

func (ConditionalDefaultService_StayUnit) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_service_proto_enumTypes[9]
}

func (x ConditionalDefaultService_StayUnit) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConditionalDefaultService_StayUnit.Descriptor instead.
func (ConditionalDefaultService_StayUnit) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{18, 1}
}

// Defines the structure for a service, which acts as a blueprint for creating specific service instances.
type Service struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Primary key ID of the service
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The type of the organization (e.g., "enterprise", "company").
	OrganizationType v1.OrganizationType `protobuf:"varint,2,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// The ID of the organization.
	OrganizationId int64 `protobuf:"varint,3,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// The ID of the care type associated with this service.
	CareTypeId int64 `protobuf:"varint,4,opt,name=care_type_id,json=careTypeId,proto3" json:"care_type_id,omitempty"`
	// The ID of the category this service.
	CategoryId *int64 `protobuf:"varint,5,opt,name=category_id,json=categoryId,proto3,oneof" json:"category_id,omitempty"`
	// Name of the service, unique within the same company
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	// Optional description of the service
	Description *string `protobuf:"bytes,7,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// A color code associated with the service for UI purposes.
	ColorCode string `protobuf:"bytes,8,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// The sorting order of the service.
	Sort *int64 `protobuf:"varint,9,opt,name=sort,proto3,oneof" json:"sort,omitempty"`
	// A list of image URLs for the service.
	Images []string `protobuf:"bytes,10,rep,name=images,proto3" json:"images,omitempty"`
	// The offering source of the service.
	Source OfferingSource `protobuf:"varint,11,opt,name=source,proto3,enum=backend.proto.offering.v1.OfferingSource" json:"source,omitempty"`
	// (-- api-linter: core::0216::synonyms=disabled
	//
	//	aip.dev/not-precedent: 保持 status 命名设计. --)
	//
	// The status of the service.
	Status Service_Status `protobuf:"varint,12,opt,name=status,proto3,enum=backend.proto.offering.v1.Service_Status" json:"status,omitempty"`
	// Is deleted
	IsDeleted bool `protobuf:"varint,13,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	// The type of the service.
	Type Service_Type `protobuf:"varint,14,opt,name=type,proto3,enum=backend.proto.offering.v1.Service_Type" json:"type,omitempty"`
	// The price of the service.
	Price *money.Money `protobuf:"bytes,15,opt,name=price,proto3" json:"price,omitempty"`
	// The related tax id of the service.
	TaxId int64 `protobuf:"varint,17,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// The timestamp when the service was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,20,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The timestamp when the service was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,21,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// The timestamp when the service was deleted.
	DeleteTime *timestamppb.Timestamp `protobuf:"bytes,22,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"`
	// The available business scope for this service.
	AvailableBusiness *AvailableBusiness `protobuf:"bytes,23,opt,name=available_business,json=availableBusiness,proto3" json:"available_business,omitempty"`
	// Additional service/addon scope configuration
	AdditionalService *AdditionalService `protobuf:"bytes,24,opt,name=additional_service,json=additionalService,proto3,oneof" json:"additional_service,omitempty"`
	// Available pet type and breed configuration
	AvailableTypeBreed *AvailablePetTypeBreed `protobuf:"bytes,25,opt,name=available_type_breed,json=availableTypeBreed,proto3,oneof" json:"available_type_breed,omitempty"`
	// Available pet size configuration
	AvailablePetSize *AvailablePetSize `protobuf:"bytes,26,opt,name=available_pet_size,json=availablePetSize,proto3,oneof" json:"available_pet_size,omitempty"`
	// Available pet coat type configuration
	AvailableCoatType *AvailableCoatType `protobuf:"bytes,27,opt,name=available_coat_type,json=availableCoatType,proto3,oneof" json:"available_coat_type,omitempty"`
	// Available pet code configuration
	AvailablePetCode *AvailablePetCode `protobuf:"bytes,28,opt,name=available_pet_code,json=availablePetCode,proto3,oneof" json:"available_pet_code,omitempty"`
	// Available pet weight configuration
	AvailablePetWeight *AvailablePetWeight `protobuf:"bytes,29,opt,name=available_pet_weight,json=availablePetWeight,proto3,oneof" json:"available_pet_weight,omitempty"`
	// Business and staff override configuration (price, duration, tax_id overrides for specific businesses and staff)
	BusinessStaffOverrides []*BusinessStaffOverride `protobuf:"bytes,30,rep,name=business_staff_overrides,json=businessStaffOverrides,proto3" json:"business_staff_overrides,omitempty"`
	// Pet override configuration (price, duration overrides for specific pets)
	PetOverrides []*PetOverride `protobuf:"bytes,31,rep,name=pet_overrides,json=petOverrides,proto3" json:"pet_overrides,omitempty"`
	// Service attributes for better frontend type safety
	Attributes    *ServiceAttributes `protobuf:"bytes,99,opt,name=attributes,proto3,oneof" json:"attributes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Service) Reset() {
	*x = Service{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Service) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Service) ProtoMessage() {}

func (x *Service) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Service.ProtoReflect.Descriptor instead.
func (*Service) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{0}
}

func (x *Service) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Service) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *Service) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *Service) GetCareTypeId() int64 {
	if x != nil {
		return x.CareTypeId
	}
	return 0
}

func (x *Service) GetCategoryId() int64 {
	if x != nil && x.CategoryId != nil {
		return *x.CategoryId
	}
	return 0
}

func (x *Service) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Service) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *Service) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *Service) GetSort() int64 {
	if x != nil && x.Sort != nil {
		return *x.Sort
	}
	return 0
}

func (x *Service) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *Service) GetSource() OfferingSource {
	if x != nil {
		return x.Source
	}
	return OfferingSource_OFFERING_SOURCE_UNSPECIFIED
}

func (x *Service) GetStatus() Service_Status {
	if x != nil {
		return x.Status
	}
	return Service_STATUS_UNSPECIFIED
}

func (x *Service) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

func (x *Service) GetType() Service_Type {
	if x != nil {
		return x.Type
	}
	return Service_TYPE_UNSPECIFIED
}

func (x *Service) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *Service) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *Service) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Service) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Service) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

func (x *Service) GetAvailableBusiness() *AvailableBusiness {
	if x != nil {
		return x.AvailableBusiness
	}
	return nil
}

func (x *Service) GetAdditionalService() *AdditionalService {
	if x != nil {
		return x.AdditionalService
	}
	return nil
}

func (x *Service) GetAvailableTypeBreed() *AvailablePetTypeBreed {
	if x != nil {
		return x.AvailableTypeBreed
	}
	return nil
}

func (x *Service) GetAvailablePetSize() *AvailablePetSize {
	if x != nil {
		return x.AvailablePetSize
	}
	return nil
}

func (x *Service) GetAvailableCoatType() *AvailableCoatType {
	if x != nil {
		return x.AvailableCoatType
	}
	return nil
}

func (x *Service) GetAvailablePetCode() *AvailablePetCode {
	if x != nil {
		return x.AvailablePetCode
	}
	return nil
}

func (x *Service) GetAvailablePetWeight() *AvailablePetWeight {
	if x != nil {
		return x.AvailablePetWeight
	}
	return nil
}

func (x *Service) GetBusinessStaffOverrides() []*BusinessStaffOverride {
	if x != nil {
		return x.BusinessStaffOverrides
	}
	return nil
}

func (x *Service) GetPetOverrides() []*PetOverride {
	if x != nil {
		return x.PetOverrides
	}
	return nil
}

func (x *Service) GetAttributes() *ServiceAttributes {
	if x != nil {
		return x.Attributes
	}
	return nil
}

// Additional service/addon scope configuration
type AdditionalService struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// List of care type IDs to include all services under those care types
	AdditionalCareTypeIds []int64 `protobuf:"varint,1,rep,packed,name=additional_care_type_ids,json=additionalCareTypeIds,proto3" json:"additional_care_type_ids,omitempty"`
	// List of specific additional service/addon IDs
	AdditionalServiceIds []int64 `protobuf:"varint,2,rep,packed,name=additional_service_ids,json=additionalServiceIds,proto3" json:"additional_service_ids,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *AdditionalService) Reset() {
	*x = AdditionalService{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdditionalService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdditionalService) ProtoMessage() {}

func (x *AdditionalService) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdditionalService.ProtoReflect.Descriptor instead.
func (*AdditionalService) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{1}
}

func (x *AdditionalService) GetAdditionalCareTypeIds() []int64 {
	if x != nil {
		return x.AdditionalCareTypeIds
	}
	return nil
}

func (x *AdditionalService) GetAdditionalServiceIds() []int64 {
	if x != nil {
		return x.AdditionalServiceIds
	}
	return nil
}

// Defines a service auto rollover.
type AutoRollover struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Enabled auto rollover
	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// The ID of the target service.
	TargetServiceId *int64 `protobuf:"varint,2,opt,name=target_service_id,json=targetServiceId,proto3,oneof" json:"target_service_id,omitempty"`
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: after_minute 表示服务后多少分钟. --)
	//
	// The number of minutes after the max duration to trigger auto rollover.
	AfterMinute   *int32 `protobuf:"varint,3,opt,name=after_minute,json=afterMinute,proto3,oneof" json:"after_minute,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AutoRollover) Reset() {
	*x = AutoRollover{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AutoRollover) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoRollover) ProtoMessage() {}

func (x *AutoRollover) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoRollover.ProtoReflect.Descriptor instead.
func (*AutoRollover) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{2}
}

func (x *AutoRollover) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *AutoRollover) GetTargetServiceId() int64 {
	if x != nil && x.TargetServiceId != nil {
		return *x.TargetServiceId
	}
	return 0
}

func (x *AutoRollover) GetAfterMinute() int32 {
	if x != nil && x.AfterMinute != nil {
		return *x.AfterMinute
	}
	return 0
}

// Defines available business configuration for a service
type AvailableBusiness struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the service is available for all businesses
	IsAll bool `protobuf:"varint,1,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// List of specific business IDs when is_all is false
	BusinessIds   []int64 `protobuf:"varint,2,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AvailableBusiness) Reset() {
	*x = AvailableBusiness{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailableBusiness) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailableBusiness) ProtoMessage() {}

func (x *AvailableBusiness) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailableBusiness.ProtoReflect.Descriptor instead.
func (*AvailableBusiness) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{3}
}

func (x *AvailableBusiness) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *AvailableBusiness) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

// Defines available staff configuration for a service
type AvailableStaff struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the service is available for all staff
	IsAll bool `protobuf:"varint,1,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// List of specific staff IDs when is_all is false
	StaffIds      []int64 `protobuf:"varint,2,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AvailableStaff) Reset() {
	*x = AvailableStaff{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailableStaff) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailableStaff) ProtoMessage() {}

func (x *AvailableStaff) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailableStaff.ProtoReflect.Descriptor instead.
func (*AvailableStaff) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{4}
}

func (x *AvailableStaff) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *AvailableStaff) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

// Defines available lodging configuration for a service
type AvailableLodgingType struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the service is available for all lodging
	IsAll bool `protobuf:"varint,1,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// List of specific lodging IDs when is_all is false
	LodgingTypeIds []int64 `protobuf:"varint,2,rep,packed,name=lodging_type_ids,json=lodgingTypeIds,proto3" json:"lodging_type_ids,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AvailableLodgingType) Reset() {
	*x = AvailableLodgingType{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailableLodgingType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailableLodgingType) ProtoMessage() {}

func (x *AvailableLodgingType) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailableLodgingType.ProtoReflect.Descriptor instead.
func (*AvailableLodgingType) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{5}
}

func (x *AvailableLodgingType) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *AvailableLodgingType) GetLodgingTypeIds() []int64 {
	if x != nil {
		return x.LodgingTypeIds
	}
	return nil
}

// Defines service attributes for better frontend type safety
type ServiceAttributes struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Duration of the service.
	Duration *int32 `protobuf:"varint,1,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	// Max duration of the service.
	MaxDuration *int32 `protobuf:"varint,2,opt,name=max_duration,json=maxDuration,proto3,oneof" json:"max_duration,omitempty"`
	// Auto rollover configuration. auto rollover will be triggered when the service duration is greater than the max duration.
	AutoRollover *AutoRollover `protobuf:"bytes,3,opt,name=auto_rollover,json=autoRollover,proto3,oneof" json:"auto_rollover,omitempty"`
	// Available staff configuration. available staff will be used to schedule the service.
	AvailableStaff *AvailableStaff `protobuf:"bytes,4,opt,name=available_staff,json=availableStaff,proto3,oneof" json:"available_staff,omitempty"`
	// Available lodging type configuration. available lodging type will be used to schedule the service.
	AvailableLodgingType *AvailableLodgingType `protobuf:"bytes,5,opt,name=available_lodging_type,json=availableLodgingType,proto3,oneof" json:"available_lodging_type,omitempty"`
	// Whether the addon requires staff (only for ADD_ON type services)
	IsRequiredStaff *bool `protobuf:"varint,6,opt,name=is_required_staff,json=isRequiredStaff,proto3,oneof" json:"is_required_staff,omitempty"`
	// Result validity period configuration (for services that produce time-sensitive results)
	ResultValidityPeriod *ResultValidityPeriod `protobuf:"bytes,7,opt,name=result_validity_period,json=resultValidityPeriod,proto3,oneof" json:"result_validity_period,omitempty"`
	// Auto assign staff (for evaluation services)
	AutoAssignStaff *bool `protobuf:"varint,8,opt,name=auto_assign_staff,json=autoAssignStaff,proto3,oneof" json:"auto_assign_staff,omitempty"`
	// Online booking alias (for evaluation services)
	ObAlias *string `protobuf:"bytes,9,opt,name=ob_alias,json=obAlias,proto3,oneof" json:"ob_alias,omitempty"`
	// Prerequisite rule configuration (for services that have prerequisite services)
	PrerequisiteRule *PrerequisiteRule `protobuf:"bytes,10,opt,name=prerequisite_rule,json=prerequisiteRule,proto3,oneof" json:"prerequisite_rule,omitempty"`
	// The billing price unit of the service.
	PriceUnit *PriceUnit `protobuf:"varint,12,opt,name=price_unit,json=priceUnit,proto3,enum=backend.proto.offering.v1.PriceUnit,oneof" json:"price_unit,omitempty"`
	// Default service configuration for single-day services
	DefaultService *DefaultService `protobuf:"bytes,13,opt,name=default_service,json=defaultService,proto3,oneof" json:"default_service,omitempty"`
	// Default service configuration for conditional cross-day services
	ConditionalDefaultService *ConditionalDefaultService `protobuf:"bytes,14,opt,name=conditional_default_service,json=conditionalDefaultService,proto3,oneof" json:"conditional_default_service,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *ServiceAttributes) Reset() {
	*x = ServiceAttributes{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceAttributes) ProtoMessage() {}

func (x *ServiceAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceAttributes.ProtoReflect.Descriptor instead.
func (*ServiceAttributes) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{6}
}

func (x *ServiceAttributes) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

func (x *ServiceAttributes) GetMaxDuration() int32 {
	if x != nil && x.MaxDuration != nil {
		return *x.MaxDuration
	}
	return 0
}

func (x *ServiceAttributes) GetAutoRollover() *AutoRollover {
	if x != nil {
		return x.AutoRollover
	}
	return nil
}

func (x *ServiceAttributes) GetAvailableStaff() *AvailableStaff {
	if x != nil {
		return x.AvailableStaff
	}
	return nil
}

func (x *ServiceAttributes) GetAvailableLodgingType() *AvailableLodgingType {
	if x != nil {
		return x.AvailableLodgingType
	}
	return nil
}

func (x *ServiceAttributes) GetIsRequiredStaff() bool {
	if x != nil && x.IsRequiredStaff != nil {
		return *x.IsRequiredStaff
	}
	return false
}

func (x *ServiceAttributes) GetResultValidityPeriod() *ResultValidityPeriod {
	if x != nil {
		return x.ResultValidityPeriod
	}
	return nil
}

func (x *ServiceAttributes) GetAutoAssignStaff() bool {
	if x != nil && x.AutoAssignStaff != nil {
		return *x.AutoAssignStaff
	}
	return false
}

func (x *ServiceAttributes) GetObAlias() string {
	if x != nil && x.ObAlias != nil {
		return *x.ObAlias
	}
	return ""
}

func (x *ServiceAttributes) GetPrerequisiteRule() *PrerequisiteRule {
	if x != nil {
		return x.PrerequisiteRule
	}
	return nil
}

func (x *ServiceAttributes) GetPriceUnit() PriceUnit {
	if x != nil && x.PriceUnit != nil {
		return *x.PriceUnit
	}
	return PriceUnit_PRICE_UNIT_UNSPECIFIED
}

func (x *ServiceAttributes) GetDefaultService() *DefaultService {
	if x != nil {
		return x.DefaultService
	}
	return nil
}

func (x *ServiceAttributes) GetConditionalDefaultService() *ConditionalDefaultService {
	if x != nil {
		return x.ConditionalDefaultService
	}
	return nil
}

// Defines prerequisite rule configuration for services that have prerequisite care types or services
type PrerequisiteRule struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Enabled for business
	Enabled *bool `protobuf:"varint,1,opt,name=enabled,proto3,oneof" json:"enabled,omitempty"`
	// Enabled for online booking
	ObEnabled *bool `protobuf:"varint,2,opt,name=ob_enabled,json=obEnabled,proto3,oneof" json:"ob_enabled,omitempty"`
	// The prerequisite services
	ServiceIds    []int64 `protobuf:"varint,3,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PrerequisiteRule) Reset() {
	*x = PrerequisiteRule{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrerequisiteRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrerequisiteRule) ProtoMessage() {}

func (x *PrerequisiteRule) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrerequisiteRule.ProtoReflect.Descriptor instead.
func (*PrerequisiteRule) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{7}
}

func (x *PrerequisiteRule) GetEnabled() bool {
	if x != nil && x.Enabled != nil {
		return *x.Enabled
	}
	return false
}

func (x *PrerequisiteRule) GetObEnabled() bool {
	if x != nil && x.ObEnabled != nil {
		return *x.ObEnabled
	}
	return false
}

func (x *PrerequisiteRule) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

// Defines result validity period configuration for time-sensitive services
type ResultValidityPeriod struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The validity period type
	PeriodType ResultValidityPeriod_ValidityPeriodType `protobuf:"varint,1,opt,name=period_type,json=periodType,proto3,enum=backend.proto.offering.v1.ResultValidityPeriod_ValidityPeriodType" json:"period_type,omitempty"`
	// The validity duration (only used when period_type is CONDITIONAL)
	Duration      *ValidityDuration `protobuf:"bytes,2,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResultValidityPeriod) Reset() {
	*x = ResultValidityPeriod{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResultValidityPeriod) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResultValidityPeriod) ProtoMessage() {}

func (x *ResultValidityPeriod) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResultValidityPeriod.ProtoReflect.Descriptor instead.
func (*ResultValidityPeriod) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{8}
}

func (x *ResultValidityPeriod) GetPeriodType() ResultValidityPeriod_ValidityPeriodType {
	if x != nil {
		return x.PeriodType
	}
	return ResultValidityPeriod_VALIDITY_PERIOD_TYPE_UNSPECIFIED
}

func (x *ResultValidityPeriod) GetDuration() *ValidityDuration {
	if x != nil {
		return x.Duration
	}
	return nil
}

// Defines validity duration configuration
type ValidityDuration struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The duration value
	Value int32 `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
	// The duration unit
	Unit          ValidityDuration_DurationUnit `protobuf:"varint,2,opt,name=unit,proto3,enum=backend.proto.offering.v1.ValidityDuration_DurationUnit" json:"unit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidityDuration) Reset() {
	*x = ValidityDuration{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidityDuration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidityDuration) ProtoMessage() {}

func (x *ValidityDuration) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidityDuration.ProtoReflect.Descriptor instead.
func (*ValidityDuration) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{9}
}

func (x *ValidityDuration) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *ValidityDuration) GetUnit() ValidityDuration_DurationUnit {
	if x != nil {
		return x.Unit
	}
	return ValidityDuration_DURATION_UNIT_UNSPECIFIED
}

// Available pet type and breed configuration
type AvailablePetTypeBreed struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the service is available for all pet types and breeds
	IsAll bool `protobuf:"varint,1,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// List of specific pet type configurations when is_all is false
	AvailablePetTypes []*AvailablePetType `protobuf:"bytes,2,rep,name=available_pet_types,json=availablePetTypes,proto3" json:"available_pet_types,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *AvailablePetTypeBreed) Reset() {
	*x = AvailablePetTypeBreed{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailablePetTypeBreed) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailablePetTypeBreed) ProtoMessage() {}

func (x *AvailablePetTypeBreed) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailablePetTypeBreed.ProtoReflect.Descriptor instead.
func (*AvailablePetTypeBreed) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{10}
}

func (x *AvailablePetTypeBreed) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *AvailablePetTypeBreed) GetAvailablePetTypes() []*AvailablePetType {
	if x != nil {
		return x.AvailablePetTypes
	}
	return nil
}

// Pet type configuration
type AvailablePetType struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The ID of the pet type
	PetTypeId int64 `protobuf:"varint,1,opt,name=pet_type_id,json=petTypeId,proto3" json:"pet_type_id,omitempty"`
	// Whether the service is available for all breeds of this pet type
	IsAll bool `protobuf:"varint,2,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// List of specific breed IDs when is_all is false
	BreedIds      []int64 `protobuf:"varint,3,rep,packed,name=breed_ids,json=breedIds,proto3" json:"breed_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AvailablePetType) Reset() {
	*x = AvailablePetType{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailablePetType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailablePetType) ProtoMessage() {}

func (x *AvailablePetType) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailablePetType.ProtoReflect.Descriptor instead.
func (*AvailablePetType) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{11}
}

func (x *AvailablePetType) GetPetTypeId() int64 {
	if x != nil {
		return x.PetTypeId
	}
	return 0
}

func (x *AvailablePetType) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *AvailablePetType) GetBreedIds() []int64 {
	if x != nil {
		return x.BreedIds
	}
	return nil
}

// Available pet size configuration
type AvailablePetSize struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the service is available for all pet sizes
	IsAll bool `protobuf:"varint,1,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// List of specific pet size IDs when is_all is false
	PetSizeIds    []int64 `protobuf:"varint,2,rep,packed,name=pet_size_ids,json=petSizeIds,proto3" json:"pet_size_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AvailablePetSize) Reset() {
	*x = AvailablePetSize{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailablePetSize) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailablePetSize) ProtoMessage() {}

func (x *AvailablePetSize) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailablePetSize.ProtoReflect.Descriptor instead.
func (*AvailablePetSize) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{12}
}

func (x *AvailablePetSize) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *AvailablePetSize) GetPetSizeIds() []int64 {
	if x != nil {
		return x.PetSizeIds
	}
	return nil
}

// Available pet coat type configuration
type AvailableCoatType struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the service is available for all pet coat types
	IsAll bool `protobuf:"varint,1,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// List of specific pet coat type IDs when is_all is false
	CoatTypeIds   []int64 `protobuf:"varint,2,rep,packed,name=coat_type_ids,json=coatTypeIds,proto3" json:"coat_type_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AvailableCoatType) Reset() {
	*x = AvailableCoatType{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailableCoatType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailableCoatType) ProtoMessage() {}

func (x *AvailableCoatType) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailableCoatType.ProtoReflect.Descriptor instead.
func (*AvailableCoatType) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{13}
}

func (x *AvailableCoatType) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *AvailableCoatType) GetCoatTypeIds() []int64 {
	if x != nil {
		return x.CoatTypeIds
	}
	return nil
}

// Available pet code configuration
type AvailablePetCode struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The rule type for pet code filtering (only used when no_restriction is false)
	RuleType AvailabilityRuleType `protobuf:"varint,1,opt,name=rule_type,json=ruleType,proto3,enum=backend.proto.offering.v1.AvailabilityRuleType" json:"rule_type,omitempty"`
	// List of pet code IDs based on the rule type
	PetCodeIds    []int64 `protobuf:"varint,2,rep,packed,name=pet_code_ids,json=petCodeIds,proto3" json:"pet_code_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AvailablePetCode) Reset() {
	*x = AvailablePetCode{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailablePetCode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailablePetCode) ProtoMessage() {}

func (x *AvailablePetCode) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailablePetCode.ProtoReflect.Descriptor instead.
func (*AvailablePetCode) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{14}
}

func (x *AvailablePetCode) GetRuleType() AvailabilityRuleType {
	if x != nil {
		return x.RuleType
	}
	return AvailabilityRuleType_AVAILABILITY_RULE_TYPE_UNSPECIFIED
}

func (x *AvailablePetCode) GetPetCodeIds() []int64 {
	if x != nil {
		return x.PetCodeIds
	}
	return nil
}

// Available pet weight configuration
type AvailablePetWeight struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the service is available for all pet weights
	IsAll bool `protobuf:"varint,1,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// List of specific pet weight ranges when is_all is false
	PetWeightRanges []*PetWeightRange `protobuf:"bytes,2,rep,name=pet_weight_ranges,json=petWeightRanges,proto3" json:"pet_weight_ranges,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *AvailablePetWeight) Reset() {
	*x = AvailablePetWeight{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailablePetWeight) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailablePetWeight) ProtoMessage() {}

func (x *AvailablePetWeight) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailablePetWeight.ProtoReflect.Descriptor instead.
func (*AvailablePetWeight) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{15}
}

func (x *AvailablePetWeight) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *AvailablePetWeight) GetPetWeightRanges() []*PetWeightRange {
	if x != nil {
		return x.PetWeightRanges
	}
	return nil
}

// Pet weight range configuration
type PetWeightRange struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Minimum weight in pounds (inclusive)
	MinWeight float64 `protobuf:"fixed64,1,opt,name=min_weight,json=minWeight,proto3" json:"min_weight,omitempty"`
	// Maximum weight in pounds (inclusive)
	MaxWeight     float64 `protobuf:"fixed64,2,opt,name=max_weight,json=maxWeight,proto3" json:"max_weight,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PetWeightRange) Reset() {
	*x = PetWeightRange{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetWeightRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetWeightRange) ProtoMessage() {}

func (x *PetWeightRange) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetWeightRange.ProtoReflect.Descriptor instead.
func (*PetWeightRange) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{16}
}

func (x *PetWeightRange) GetMinWeight() float64 {
	if x != nil {
		return x.MinWeight
	}
	return 0
}

func (x *PetWeightRange) GetMaxWeight() float64 {
	if x != nil {
		return x.MaxWeight
	}
	return 0
}

// Defines default service configuration for single-day services
type DefaultService struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether default service functionality is enabled
	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// The default services to apply
	ServiceIds    []int64 `protobuf:"varint,2,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DefaultService) Reset() {
	*x = DefaultService{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DefaultService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DefaultService) ProtoMessage() {}

func (x *DefaultService) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DefaultService.ProtoReflect.Descriptor instead.
func (*DefaultService) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{17}
}

func (x *DefaultService) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *DefaultService) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

// Defines default service/addon configuration for conditional cross-day services
type ConditionalDefaultService struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether default service functionality is enabled
	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// The condition for triggering default services
	Condition *ConditionalDefaultService_Condition `protobuf:"bytes,2,opt,name=condition,proto3" json:"condition,omitempty"`
	// The default services/addons to apply
	ApplyRules    []*ConditionalDefaultService_ApplyRule `protobuf:"bytes,3,rep,name=apply_rules,json=applyRules,proto3" json:"apply_rules,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConditionalDefaultService) Reset() {
	*x = ConditionalDefaultService{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConditionalDefaultService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConditionalDefaultService) ProtoMessage() {}

func (x *ConditionalDefaultService) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConditionalDefaultService.ProtoReflect.Descriptor instead.
func (*ConditionalDefaultService) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{18}
}

func (x *ConditionalDefaultService) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *ConditionalDefaultService) GetCondition() *ConditionalDefaultService_Condition {
	if x != nil {
		return x.Condition
	}
	return nil
}

func (x *ConditionalDefaultService) GetApplyRules() []*ConditionalDefaultService_ApplyRule {
	if x != nil {
		return x.ApplyRules
	}
	return nil
}

// Business override and staff override
type BusinessStaffOverride struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Business override
	BusinessOverride *BusinessOverride `protobuf:"bytes,1,opt,name=business_override,json=businessOverride,proto3" json:"business_override,omitempty"`
	// Staff override
	StaffOverrides []*StaffOverride `protobuf:"bytes,2,rep,name=staff_overrides,json=staffOverrides,proto3" json:"staff_overrides,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BusinessStaffOverride) Reset() {
	*x = BusinessStaffOverride{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BusinessStaffOverride) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessStaffOverride) ProtoMessage() {}

func (x *BusinessStaffOverride) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessStaffOverride.ProtoReflect.Descriptor instead.
func (*BusinessStaffOverride) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{19}
}

func (x *BusinessStaffOverride) GetBusinessOverride() *BusinessOverride {
	if x != nil {
		return x.BusinessOverride
	}
	return nil
}

func (x *BusinessStaffOverride) GetStaffOverrides() []*StaffOverride {
	if x != nil {
		return x.StaffOverrides
	}
	return nil
}

// Business override configuration for service pricing and settings
type BusinessOverride struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The business ID this override applies to
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Price
	Price *money.Money `protobuf:"bytes,2,opt,name=price,proto3,oneof" json:"price,omitempty"`
	// Tax ID
	TaxId *int64 `protobuf:"varint,3,opt,name=tax_id,json=taxId,proto3,oneof" json:"tax_id,omitempty"`
	// Duration
	Duration *int32 `protobuf:"varint,4,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	// Max duration
	MaxDuration   *int32 `protobuf:"varint,5,opt,name=max_duration,json=maxDuration,proto3,oneof" json:"max_duration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BusinessOverride) Reset() {
	*x = BusinessOverride{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BusinessOverride) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessOverride) ProtoMessage() {}

func (x *BusinessOverride) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessOverride.ProtoReflect.Descriptor instead.
func (*BusinessOverride) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{20}
}

func (x *BusinessOverride) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BusinessOverride) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *BusinessOverride) GetTaxId() int64 {
	if x != nil && x.TaxId != nil {
		return *x.TaxId
	}
	return 0
}

func (x *BusinessOverride) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

func (x *BusinessOverride) GetMaxDuration() int32 {
	if x != nil && x.MaxDuration != nil {
		return *x.MaxDuration
	}
	return 0
}

// Staff override configuration for service pricing and settings
type StaffOverride struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The staff ID this override applies to
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// The business ID this staff override applies to
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Price
	Price *money.Money `protobuf:"bytes,3,opt,name=price,proto3,oneof" json:"price,omitempty"`
	// Duration
	Duration      *int32 `protobuf:"varint,4,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StaffOverride) Reset() {
	*x = StaffOverride{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StaffOverride) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffOverride) ProtoMessage() {}

func (x *StaffOverride) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffOverride.ProtoReflect.Descriptor instead.
func (*StaffOverride) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{21}
}

func (x *StaffOverride) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *StaffOverride) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *StaffOverride) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *StaffOverride) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

// Override values for price, duration, and tax settings
type OverrideValues struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Price override (optional)
	Price *money.Money `protobuf:"bytes,1,opt,name=price,proto3,oneof" json:"price,omitempty"`
	// Duration override (optional, only applicable for daycare and grooming care types)
	Duration *int32 `protobuf:"varint,2,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	// Tax ID override (optional)
	TaxId *int64 `protobuf:"varint,3,opt,name=tax_id,json=taxId,proto3,oneof" json:"tax_id,omitempty"`
	// Max duration override (optional)
	MaxDuration   *int32 `protobuf:"varint,4,opt,name=max_duration,json=maxDuration,proto3,oneof" json:"max_duration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OverrideValues) Reset() {
	*x = OverrideValues{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OverrideValues) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OverrideValues) ProtoMessage() {}

func (x *OverrideValues) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OverrideValues.ProtoReflect.Descriptor instead.
func (*OverrideValues) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{22}
}

func (x *OverrideValues) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *OverrideValues) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

func (x *OverrideValues) GetTaxId() int64 {
	if x != nil && x.TaxId != nil {
		return *x.TaxId
	}
	return 0
}

func (x *OverrideValues) GetMaxDuration() int32 {
	if x != nil && x.MaxDuration != nil {
		return *x.MaxDuration
	}
	return 0
}

// Pet override configuration for service pricing and settings
type PetOverride struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// pet override id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The pet ID this override applies to
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// The service ID this pet override applies to
	ServiceId int64 `protobuf:"varint,3,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// Price
	Price *money.Money `protobuf:"bytes,4,opt,name=price,proto3,oneof" json:"price,omitempty"`
	// Duration override (optional, only applicable for daycare and grooming care types)
	Duration      *int32 `protobuf:"varint,5,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PetOverride) Reset() {
	*x = PetOverride{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetOverride) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetOverride) ProtoMessage() {}

func (x *PetOverride) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetOverride.ProtoReflect.Descriptor instead.
func (*PetOverride) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{23}
}

func (x *PetOverride) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PetOverride) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetOverride) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *PetOverride) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *PetOverride) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

// customized service
type CustomizedService struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// service
	Service *Service `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	// price
	Price *money.Money `protobuf:"bytes,2,opt,name=price,proto3" json:"price,omitempty"`
	// tax id
	TaxId int64 `protobuf:"varint,3,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// duration
	Duration *int32 `protobuf:"varint,4,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	// max duration
	MaxDuration *int32 `protobuf:"varint,5,opt,name=max_duration,json=maxDuration,proto3,oneof" json:"max_duration,omitempty"`
	// price override type
	PriceOverrideType OverrideType `protobuf:"varint,6,opt,name=price_override_type,json=priceOverrideType,proto3,enum=backend.proto.offering.v1.OverrideType" json:"price_override_type,omitempty"`
	// duration override type
	DurationOverrideType OverrideType `protobuf:"varint,7,opt,name=duration_override_type,json=durationOverrideType,proto3,enum=backend.proto.offering.v1.OverrideType" json:"duration_override_type,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *CustomizedService) Reset() {
	*x = CustomizedService{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomizedService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomizedService) ProtoMessage() {}

func (x *CustomizedService) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomizedService.ProtoReflect.Descriptor instead.
func (*CustomizedService) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{24}
}

func (x *CustomizedService) GetService() *Service {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *CustomizedService) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *CustomizedService) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *CustomizedService) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

func (x *CustomizedService) GetMaxDuration() int32 {
	if x != nil && x.MaxDuration != nil {
		return *x.MaxDuration
	}
	return 0
}

func (x *CustomizedService) GetPriceOverrideType() OverrideType {
	if x != nil {
		return x.PriceOverrideType
	}
	return OverrideType_OVERRIDE_TYPE_UNSPECIFIED
}

func (x *CustomizedService) GetDurationOverrideType() OverrideType {
	if x != nil {
		return x.DurationOverrideType
	}
	return OverrideType_OVERRIDE_TYPE_UNSPECIFIED
}

// Defines the condition for triggering default services
type ConditionalDefaultService_Condition struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The minimum stay length (nights/days) to trigger default services
	MinStayLength int32 `protobuf:"varint,1,opt,name=min_stay_length,json=minStayLength,proto3" json:"min_stay_length,omitempty"`
	// The stay length unit
	Unit          ConditionalDefaultService_StayUnit `protobuf:"varint,2,opt,name=unit,proto3,enum=backend.proto.offering.v1.ConditionalDefaultService_StayUnit" json:"unit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConditionalDefaultService_Condition) Reset() {
	*x = ConditionalDefaultService_Condition{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConditionalDefaultService_Condition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConditionalDefaultService_Condition) ProtoMessage() {}

func (x *ConditionalDefaultService_Condition) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConditionalDefaultService_Condition.ProtoReflect.Descriptor instead.
func (*ConditionalDefaultService_Condition) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{18, 0}
}

func (x *ConditionalDefaultService_Condition) GetMinStayLength() int32 {
	if x != nil {
		return x.MinStayLength
	}
	return 0
}

func (x *ConditionalDefaultService_Condition) GetUnit() ConditionalDefaultService_StayUnit {
	if x != nil {
		return x.Unit
	}
	return ConditionalDefaultService_STAY_UNIT_UNSPECIFIED
}

// Defines how a default service/addon should be applied
type ConditionalDefaultService_ApplyRule struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The service/addon ID to bundle
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// The date type for applying this service
	DateType ConditionalDefaultService_DateType `protobuf:"varint,2,opt,name=date_type,json=dateType,proto3,enum=backend.proto.offering.v1.ConditionalDefaultService_DateType" json:"date_type,omitempty"`
	// The quantity per day (only used for add-on)
	QuantityPerDay *int32 `protobuf:"varint,3,opt,name=quantity_per_day,json=quantityPerDay,proto3,oneof" json:"quantity_per_day,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ConditionalDefaultService_ApplyRule) Reset() {
	*x = ConditionalDefaultService_ApplyRule{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConditionalDefaultService_ApplyRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConditionalDefaultService_ApplyRule) ProtoMessage() {}

func (x *ConditionalDefaultService_ApplyRule) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConditionalDefaultService_ApplyRule.ProtoReflect.Descriptor instead.
func (*ConditionalDefaultService_ApplyRule) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{18, 1}
}

func (x *ConditionalDefaultService_ApplyRule) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *ConditionalDefaultService_ApplyRule) GetDateType() ConditionalDefaultService_DateType {
	if x != nil {
		return x.DateType
	}
	return ConditionalDefaultService_DATE_TYPE_UNSPECIFIED
}

func (x *ConditionalDefaultService_ApplyRule) GetQuantityPerDay() int32 {
	if x != nil && x.QuantityPerDay != nil {
		return *x.QuantityPerDay
	}
	return 0
}

var File_backend_proto_offering_v1_service_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_service_proto_rawDesc = "" +
	"\n" +
	"'backend/proto/offering/v1/service.proto\x12\x19backend.proto.offering.v1\x1a&backend/proto/offering/v1/common.proto\x1a2backend/proto/offering/v1/service_ob_setting.proto\x1a0backend/proto/organization/v1/organization.proto\x1a\x1bbuf/validate/validate.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17google/type/money.proto\"\xd9\x10\n" +
	"\aService\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\\\n" +
	"\x11organization_type\x18\x02 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeR\x10organizationType\x12'\n" +
	"\x0forganization_id\x18\x03 \x01(\x03R\x0eorganizationId\x12 \n" +
	"\fcare_type_id\x18\x04 \x01(\x03R\n" +
	"careTypeId\x12$\n" +
	"\vcategory_id\x18\x05 \x01(\x03H\x00R\n" +
	"categoryId\x88\x01\x01\x12\x12\n" +
	"\x04name\x18\x06 \x01(\tR\x04name\x12%\n" +
	"\vdescription\x18\a \x01(\tH\x01R\vdescription\x88\x01\x01\x12\x1d\n" +
	"\n" +
	"color_code\x18\b \x01(\tR\tcolorCode\x12\x17\n" +
	"\x04sort\x18\t \x01(\x03H\x02R\x04sort\x88\x01\x01\x12\x16\n" +
	"\x06images\x18\n" +
	" \x03(\tR\x06images\x12A\n" +
	"\x06source\x18\v \x01(\x0e2).backend.proto.offering.v1.OfferingSourceR\x06source\x12A\n" +
	"\x06status\x18\f \x01(\x0e2).backend.proto.offering.v1.Service.StatusR\x06status\x12\x1d\n" +
	"\n" +
	"is_deleted\x18\r \x01(\bR\tisDeleted\x12;\n" +
	"\x04type\x18\x0e \x01(\x0e2'.backend.proto.offering.v1.Service.TypeR\x04type\x12(\n" +
	"\x05price\x18\x0f \x01(\v2\x12.google.type.MoneyR\x05price\x12\x15\n" +
	"\x06tax_id\x18\x11 \x01(\x03R\x05taxId\x12;\n" +
	"\vcreate_time\x18\x14 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x15 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12@\n" +
	"\vdelete_time\x18\x16 \x01(\v2\x1a.google.protobuf.TimestampH\x03R\n" +
	"deleteTime\x88\x01\x01\x12[\n" +
	"\x12available_business\x18\x17 \x01(\v2,.backend.proto.offering.v1.AvailableBusinessR\x11availableBusiness\x12`\n" +
	"\x12additional_service\x18\x18 \x01(\v2,.backend.proto.offering.v1.AdditionalServiceH\x04R\x11additionalService\x88\x01\x01\x12g\n" +
	"\x14available_type_breed\x18\x19 \x01(\v20.backend.proto.offering.v1.AvailablePetTypeBreedH\x05R\x12availableTypeBreed\x88\x01\x01\x12^\n" +
	"\x12available_pet_size\x18\x1a \x01(\v2+.backend.proto.offering.v1.AvailablePetSizeH\x06R\x10availablePetSize\x88\x01\x01\x12a\n" +
	"\x13available_coat_type\x18\x1b \x01(\v2,.backend.proto.offering.v1.AvailableCoatTypeH\aR\x11availableCoatType\x88\x01\x01\x12^\n" +
	"\x12available_pet_code\x18\x1c \x01(\v2+.backend.proto.offering.v1.AvailablePetCodeH\bR\x10availablePetCode\x88\x01\x01\x12d\n" +
	"\x14available_pet_weight\x18\x1d \x01(\v2-.backend.proto.offering.v1.AvailablePetWeightH\tR\x12availablePetWeight\x88\x01\x01\x12j\n" +
	"\x18business_staff_overrides\x18\x1e \x03(\v20.backend.proto.offering.v1.BusinessStaffOverrideR\x16businessStaffOverrides\x12K\n" +
	"\rpet_overrides\x18\x1f \x03(\v2&.backend.proto.offering.v1.PetOverrideR\fpetOverrides\x12Q\n" +
	"\n" +
	"attributes\x18c \x01(\v2,.backend.proto.offering.v1.ServiceAttributesH\n" +
	"R\n" +
	"attributes\x88\x01\x01\":\n" +
	"\x06Status\x12\x16\n" +
	"\x12STATUS_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x01\x12\f\n" +
	"\bINACTIVE\x10\x02\"5\n" +
	"\x04Type\x12\x14\n" +
	"\x10TYPE_UNSPECIFIED\x10\x00\x12\v\n" +
	"\aSERVICE\x10\x01\x12\n" +
	"\n" +
	"\x06ADD_ON\x10\x02B\x0e\n" +
	"\f_category_idB\x0e\n" +
	"\f_descriptionB\a\n" +
	"\x05_sortB\x0e\n" +
	"\f_delete_timeB\x15\n" +
	"\x13_additional_serviceB\x17\n" +
	"\x15_available_type_breedB\x15\n" +
	"\x13_available_pet_sizeB\x16\n" +
	"\x14_available_coat_typeB\x15\n" +
	"\x13_available_pet_codeB\x17\n" +
	"\x15_available_pet_weightB\r\n" +
	"\v_attributes\"\xa7\x01\n" +
	"\x11AdditionalService\x12I\n" +
	"\x18additional_care_type_ids\x18\x01 \x03(\x03B\x10\xbaH\r\x92\x01\n" +
	"\b\x00\x10d\"\x04\"\x02 \x00R\x15additionalCareTypeIds\x12G\n" +
	"\x16additional_service_ids\x18\x02 \x03(\x03B\x11\xbaH\x0e\x92\x01\v\b\x00\x10\xe8\a\"\x04\"\x02 \x00R\x14additionalServiceIds\"\xba\x01\n" +
	"\fAutoRollover\x12\x18\n" +
	"\aenabled\x18\x01 \x01(\bR\aenabled\x128\n" +
	"\x11target_service_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x00R\x0ftargetServiceId\x88\x01\x01\x12/\n" +
	"\fafter_minute\x18\x03 \x01(\x05B\a\xbaH\x04\x1a\x02(\x00H\x01R\vafterMinute\x88\x01\x01B\x14\n" +
	"\x12_target_service_idB\x0f\n" +
	"\r_after_minute\"`\n" +
	"\x11AvailableBusiness\x12\x15\n" +
	"\x06is_all\x18\x01 \x01(\bR\x05isAll\x124\n" +
	"\fbusiness_ids\x18\x02 \x03(\x03B\x11\xbaH\x0e\x92\x01\v\b\x00\x10\x90N\"\x04\"\x02 \x00R\vbusinessIds\"D\n" +
	"\x0eAvailableStaff\x12\x15\n" +
	"\x06is_all\x18\x01 \x01(\bR\x05isAll\x12\x1b\n" +
	"\tstaff_ids\x18\x02 \x03(\x03R\bstaffIds\"W\n" +
	"\x14AvailableLodgingType\x12\x15\n" +
	"\x06is_all\x18\x01 \x01(\bR\x05isAll\x12(\n" +
	"\x10lodging_type_ids\x18\x02 \x03(\x03R\x0elodgingTypeIds\"\xfd\t\n" +
	"\x11ServiceAttributes\x12(\n" +
	"\bduration\x18\x01 \x01(\x05B\a\xbaH\x04\x1a\x02(\x00H\x00R\bduration\x88\x01\x01\x12/\n" +
	"\fmax_duration\x18\x02 \x01(\x05B\a\xbaH\x04\x1a\x02(\x00H\x01R\vmaxDuration\x88\x01\x01\x12Q\n" +
	"\rauto_rollover\x18\x03 \x01(\v2'.backend.proto.offering.v1.AutoRolloverH\x02R\fautoRollover\x88\x01\x01\x12W\n" +
	"\x0favailable_staff\x18\x04 \x01(\v2).backend.proto.offering.v1.AvailableStaffH\x03R\x0eavailableStaff\x88\x01\x01\x12j\n" +
	"\x16available_lodging_type\x18\x05 \x01(\v2/.backend.proto.offering.v1.AvailableLodgingTypeH\x04R\x14availableLodgingType\x88\x01\x01\x12/\n" +
	"\x11is_required_staff\x18\x06 \x01(\bH\x05R\x0fisRequiredStaff\x88\x01\x01\x12j\n" +
	"\x16result_validity_period\x18\a \x01(\v2/.backend.proto.offering.v1.ResultValidityPeriodH\x06R\x14resultValidityPeriod\x88\x01\x01\x12/\n" +
	"\x11auto_assign_staff\x18\b \x01(\bH\aR\x0fautoAssignStaff\x88\x01\x01\x12\x1e\n" +
	"\bob_alias\x18\t \x01(\tH\bR\aobAlias\x88\x01\x01\x12]\n" +
	"\x11prerequisite_rule\x18\n" +
	" \x01(\v2+.backend.proto.offering.v1.PrerequisiteRuleH\tR\x10prerequisiteRule\x88\x01\x01\x12H\n" +
	"\n" +
	"price_unit\x18\f \x01(\x0e2$.backend.proto.offering.v1.PriceUnitH\n" +
	"R\tpriceUnit\x88\x01\x01\x12W\n" +
	"\x0fdefault_service\x18\r \x01(\v2).backend.proto.offering.v1.DefaultServiceH\vR\x0edefaultService\x88\x01\x01\x12y\n" +
	"\x1bconditional_default_service\x18\x0e \x01(\v24.backend.proto.offering.v1.ConditionalDefaultServiceH\fR\x19conditionalDefaultService\x88\x01\x01B\v\n" +
	"\t_durationB\x0f\n" +
	"\r_max_durationB\x10\n" +
	"\x0e_auto_rolloverB\x12\n" +
	"\x10_available_staffB\x19\n" +
	"\x17_available_lodging_typeB\x14\n" +
	"\x12_is_required_staffB\x19\n" +
	"\x17_result_validity_periodB\x14\n" +
	"\x12_auto_assign_staffB\v\n" +
	"\t_ob_aliasB\x14\n" +
	"\x12_prerequisite_ruleB\r\n" +
	"\v_price_unitB\x12\n" +
	"\x10_default_serviceB\x1e\n" +
	"\x1c_conditional_default_service\"\xa4\x01\n" +
	"\x10PrerequisiteRule\x12\x1d\n" +
	"\aenabled\x18\x01 \x01(\bH\x00R\aenabled\x88\x01\x01\x12\"\n" +
	"\n" +
	"ob_enabled\x18\x02 \x01(\bH\x01R\tobEnabled\x88\x01\x01\x122\n" +
	"\vservice_ids\x18\x03 \x03(\x03B\x11\xbaH\x0e\x92\x01\v\b\x00\x10\xe8\a\"\x04\"\x02 \x00R\n" +
	"serviceIdsB\n" +
	"\n" +
	"\b_enabledB\r\n" +
	"\v_ob_enabled\"\xc1\x02\n" +
	"\x14ResultValidityPeriod\x12o\n" +
	"\vperiod_type\x18\x01 \x01(\x0e2B.backend.proto.offering.v1.ResultValidityPeriod.ValidityPeriodTypeB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00R\n" +
	"periodType\x12L\n" +
	"\bduration\x18\x02 \x01(\v2+.backend.proto.offering.v1.ValidityDurationH\x00R\bduration\x88\x01\x01\"]\n" +
	"\x12ValidityPeriodType\x12$\n" +
	" VALIDITY_PERIOD_TYPE_UNSPECIFIED\x10\x00\x12\x10\n" +
	"\fALWAYS_VALID\x10\x01\x12\x0f\n" +
	"\vCONDITIONAL\x10\x02B\v\n" +
	"\t_duration\"\xe6\x01\n" +
	"\x10ValidityDuration\x12\x1d\n" +
	"\x05value\x18\x01 \x01(\x05B\a\xbaH\x04\x1a\x02(\x01R\x05value\x12X\n" +
	"\x04unit\x18\x02 \x01(\x0e28.backend.proto.offering.v1.ValidityDuration.DurationUnitB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00R\x04unit\"Y\n" +
	"\fDurationUnit\x12\x1d\n" +
	"\x19DURATION_UNIT_UNSPECIFIED\x10\x00\x12\b\n" +
	"\x04DAYS\x10\x01\x12\t\n" +
	"\x05WEEKS\x10\x02\x12\n" +
	"\n" +
	"\x06MONTHS\x10\x03\x12\t\n" +
	"\x05YEARS\x10\x04\"\x8b\x01\n" +
	"\x15AvailablePetTypeBreed\x12\x15\n" +
	"\x06is_all\x18\x01 \x01(\bR\x05isAll\x12[\n" +
	"\x13available_pet_types\x18\x02 \x03(\v2+.backend.proto.offering.v1.AvailablePetTypeR\x11availablePetTypes\"\x82\x01\n" +
	"\x10AvailablePetType\x12'\n" +
	"\vpet_type_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tpetTypeId\x12\x15\n" +
	"\x06is_all\x18\x02 \x01(\bR\x05isAll\x12.\n" +
	"\tbreed_ids\x18\x03 \x03(\x03B\x11\xbaH\x0e\x92\x01\v\b\x00\x10\x90N\"\x04\"\x02 \x00R\bbreedIds\"^\n" +
	"\x10AvailablePetSize\x12\x15\n" +
	"\x06is_all\x18\x01 \x01(\bR\x05isAll\x123\n" +
	"\fpet_size_ids\x18\x02 \x03(\x03B\x11\xbaH\x0e\x92\x01\v\b\x00\x10\x90N\"\x04\"\x02 \x00R\n" +
	"petSizeIds\"a\n" +
	"\x11AvailableCoatType\x12\x15\n" +
	"\x06is_all\x18\x01 \x01(\bR\x05isAll\x125\n" +
	"\rcoat_type_ids\x18\x02 \x03(\x03B\x11\xbaH\x0e\x92\x01\v\b\x00\x10\x90N\"\x04\"\x02 \x00R\vcoatTypeIds\"\xa1\x01\n" +
	"\x10AvailablePetCode\x12X\n" +
	"\trule_type\x18\x01 \x01(\x0e2/.backend.proto.offering.v1.AvailabilityRuleTypeB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00R\bruleType\x123\n" +
	"\fpet_code_ids\x18\x02 \x03(\x03B\x11\xbaH\x0e\x92\x01\v\b\x00\x10\x90N\"\x04\"\x02 \x00R\n" +
	"petCodeIds\"\x82\x01\n" +
	"\x12AvailablePetWeight\x12\x15\n" +
	"\x06is_all\x18\x01 \x01(\bR\x05isAll\x12U\n" +
	"\x11pet_weight_ranges\x18\x02 \x03(\v2).backend.proto.offering.v1.PetWeightRangeR\x0fpetWeightRanges\"n\n" +
	"\x0ePetWeightRange\x12-\n" +
	"\n" +
	"min_weight\x18\x01 \x01(\x01B\x0e\xbaH\v\x12\t)\x00\x00\x00\x00\x00\x00\x00\x00R\tminWeight\x12-\n" +
	"\n" +
	"max_weight\x18\x02 \x01(\x01B\x0e\xbaH\v\x12\t!\x00\x00\x00\x00\x00\x00\x00\x00R\tmaxWeight\"^\n" +
	"\x0eDefaultService\x12\x18\n" +
	"\aenabled\x18\x01 \x01(\bR\aenabled\x122\n" +
	"\vservice_ids\x18\x02 \x03(\x03B\x11\xbaH\x0e\x92\x01\v\b\x00\x10\xe8\a\"\x04\"\x02 \x00R\n" +
	"serviceIds\"\xdd\x06\n" +
	"\x19ConditionalDefaultService\x12\x18\n" +
	"\aenabled\x18\x01 \x01(\bR\aenabled\x12\\\n" +
	"\tcondition\x18\x02 \x01(\v2>.backend.proto.offering.v1.ConditionalDefaultService.ConditionR\tcondition\x12l\n" +
	"\vapply_rules\x18\x03 \x03(\v2>.backend.proto.offering.v1.ConditionalDefaultService.ApplyRuleB\v\xbaH\b\x92\x01\x05\b\x00\x10\xe8\aR\n" +
	"applyRules\x1a\x9b\x01\n" +
	"\tCondition\x12/\n" +
	"\x0fmin_stay_length\x18\x01 \x01(\x05B\a\xbaH\x04\x1a\x02(\x01R\rminStayLength\x12]\n" +
	"\x04unit\x18\x02 \x01(\x0e2=.backend.proto.offering.v1.ConditionalDefaultService.StayUnitB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00R\x04unit\x1a\xe8\x01\n" +
	"\tApplyRule\x12&\n" +
	"\n" +
	"service_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tserviceId\x12f\n" +
	"\tdate_type\x18\x02 \x01(\x0e2=.backend.proto.offering.v1.ConditionalDefaultService.DateTypeB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00R\bdateType\x126\n" +
	"\x10quantity_per_day\x18\x03 \x01(\x05B\a\xbaH\x04\x1a\x02(\x01H\x00R\x0equantityPerDay\x88\x01\x01B\x13\n" +
	"\x11_quantity_per_day\"\x93\x01\n" +
	"\bDateType\x12\x19\n" +
	"\x15DATE_TYPE_UNSPECIFIED\x10\x00\x12 \n" +
	"\x1cEVERYDAY_EXCEPT_CHECKOUT_DAY\x10\x01\x12\f\n" +
	"\bEVERYDAY\x10\x04\x12\x1f\n" +
	"\x1bEVERYDAY_EXCEPT_CHECKIN_DAY\x10\x05\x12\f\n" +
	"\bLAST_DAY\x10\x06\x12\r\n" +
	"\tFIRST_DAY\x10\a\";\n" +
	"\bStayUnit\x12\x19\n" +
	"\x15STAY_UNIT_UNSPECIFIED\x10\x00\x12\b\n" +
	"\x04DAYS\x10\x01\x12\n" +
	"\n" +
	"\x06NIGHTS\x10\x02\"\xc4\x01\n" +
	"\x15BusinessStaffOverride\x12X\n" +
	"\x11business_override\x18\x01 \x01(\v2+.backend.proto.offering.v1.BusinessOverrideR\x10businessOverride\x12Q\n" +
	"\x0fstaff_overrides\x18\x02 \x03(\v2(.backend.proto.offering.v1.StaffOverrideR\x0estaffOverrides\"\x9e\x02\n" +
	"\x10BusinessOverride\x12(\n" +
	"\vbusiness_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"businessId\x12-\n" +
	"\x05price\x18\x02 \x01(\v2\x12.google.type.MoneyH\x00R\x05price\x88\x01\x01\x12#\n" +
	"\x06tax_id\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x01R\x05taxId\x88\x01\x01\x12(\n" +
	"\bduration\x18\x04 \x01(\x05B\a\xbaH\x04\x1a\x02(\x00H\x02R\bduration\x88\x01\x01\x12/\n" +
	"\fmax_duration\x18\x05 \x01(\x05B\a\xbaH\x04\x1a\x02(\x00H\x03R\vmaxDuration\x88\x01\x01B\b\n" +
	"\x06_priceB\t\n" +
	"\a_tax_idB\v\n" +
	"\t_durationB\x0f\n" +
	"\r_max_duration\"\xcd\x01\n" +
	"\rStaffOverride\x12\"\n" +
	"\bstaff_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\astaffId\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"businessId\x12-\n" +
	"\x05price\x18\x03 \x01(\v2\x12.google.type.MoneyH\x00R\x05price\x88\x01\x01\x12(\n" +
	"\bduration\x18\x04 \x01(\x05B\a\xbaH\x04\x1a\x02(\x00H\x01R\bduration\x88\x01\x01B\b\n" +
	"\x06_priceB\v\n" +
	"\t_duration\"\xf2\x01\n" +
	"\x0eOverrideValues\x12-\n" +
	"\x05price\x18\x01 \x01(\v2\x12.google.type.MoneyH\x00R\x05price\x88\x01\x01\x12(\n" +
	"\bduration\x18\x02 \x01(\x05B\a\xbaH\x04\x1a\x02(\x00H\x01R\bduration\x88\x01\x01\x12#\n" +
	"\x06tax_id\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x02R\x05taxId\x88\x01\x01\x12/\n" +
	"\fmax_duration\x18\x04 \x01(\x05B\a\xbaH\x04\x1a\x02(\x00H\x03R\vmaxDuration\x88\x01\x01B\b\n" +
	"\x06_priceB\v\n" +
	"\t_durationB\t\n" +
	"\a_tax_idB\x0f\n" +
	"\r_max_duration\"\xd5\x01\n" +
	"\vPetOverride\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\x12\x1e\n" +
	"\x06pet_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x05petId\x12&\n" +
	"\n" +
	"service_id\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tserviceId\x12-\n" +
	"\x05price\x18\x04 \x01(\v2\x12.google.type.MoneyH\x00R\x05price\x88\x01\x01\x12\x1f\n" +
	"\bduration\x18\x05 \x01(\x05H\x01R\bduration\x88\x01\x01B\b\n" +
	"\x06_priceB\v\n" +
	"\t_duration\"\xb1\x03\n" +
	"\x11CustomizedService\x12<\n" +
	"\aservice\x18\x01 \x01(\v2\".backend.proto.offering.v1.ServiceR\aservice\x12(\n" +
	"\x05price\x18\x02 \x01(\v2\x12.google.type.MoneyR\x05price\x12\x15\n" +
	"\x06tax_id\x18\x03 \x01(\x03R\x05taxId\x12\x1f\n" +
	"\bduration\x18\x04 \x01(\x05H\x00R\bduration\x88\x01\x01\x12&\n" +
	"\fmax_duration\x18\x05 \x01(\x05H\x01R\vmaxDuration\x88\x01\x01\x12W\n" +
	"\x13price_override_type\x18\x06 \x01(\x0e2'.backend.proto.offering.v1.OverrideTypeR\x11priceOverrideType\x12]\n" +
	"\x16duration_override_type\x18\a \x01(\x0e2'.backend.proto.offering.v1.OverrideTypeR\x14durationOverrideTypeB\v\n" +
	"\t_durationB\x0f\n" +
	"\r_max_duration*Y\n" +
	"\fOverrideType\x12\x1d\n" +
	"\x19OVERRIDE_TYPE_UNSPECIFIED\x10\x00\x12\f\n" +
	"\bBUSINESS\x10\x01\x12\a\n" +
	"\x03PET\x10\x02\x12\t\n" +
	"\x05STAFF\x10\x03\x12\b\n" +
	"\x04ZONE\x10\x04*\xaa\x02\n" +
	"\x18PetAvailabilityScopeType\x12+\n" +
	"'PET_AVAILABILITY_SCOPE_TYPE_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14ALL_TYPES_AND_BREEDS\x10\x01\x12\x11\n" +
	"\rSPECIFIC_TYPE\x10\x02\x12\x12\n" +
	"\x0eSPECIFIC_BREED\x10\x03\x12\r\n" +
	"\tALL_SIZES\x10\v\x12\x11\n" +
	"\rSPECIFIC_SIZE\x10\f\x12\x12\n" +
	"\x0eALL_COAT_TYPES\x10\x15\x12\x16\n" +
	"\x12SPECIFIC_COAT_TYPE\x10\x16\x12\r\n" +
	"\tALL_CODES\x10\x1f\x12\x11\n" +
	"\rSPECIFIC_CODE\x10 \x12\x15\n" +
	"\x11EXCLUDE_ALL_CODES\x10!\x12\x19\n" +
	"\x15EXCLUDE_SPECIFIC_CODE\x10\"*l\n" +
	"\x14AvailabilityRuleType\x12&\n" +
	"\"AVAILABILITY_RULE_TYPE_UNSPECIFIED\x10\x00\x12\x12\n" +
	"\x0eNO_RESTRICTION\x10\x01\x12\v\n" +
	"\aINCLUDE\x10\x02\x12\v\n" +
	"\aEXCLUDE\x10\x03*b\n" +
	"\tPriceUnit\x12\x1a\n" +
	"\x16PRICE_UNIT_UNSPECIFIED\x10\x00\x12\x0f\n" +
	"\vPER_SESSION\x10\x01\x12\r\n" +
	"\tPER_NIGHT\x10\x02\x12\f\n" +
	"\bPER_HOUR\x10\x03\x12\v\n" +
	"\aPER_DAY\x10\x04Bk\n" +
	"#com.moego.backend.proto.offering.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_service_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_service_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_service_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_proto_rawDesc), len(file_backend_proto_offering_v1_service_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_service_proto_rawDescData
}

var file_backend_proto_offering_v1_service_proto_enumTypes = make([]protoimpl.EnumInfo, 10)
var file_backend_proto_offering_v1_service_proto_msgTypes = make([]protoimpl.MessageInfo, 27)
var file_backend_proto_offering_v1_service_proto_goTypes = []any{
	(OverrideType)(0),                            // 0: backend.proto.offering.v1.OverrideType
	(PetAvailabilityScopeType)(0),                // 1: backend.proto.offering.v1.PetAvailabilityScopeType
	(AvailabilityRuleType)(0),                    // 2: backend.proto.offering.v1.AvailabilityRuleType
	(PriceUnit)(0),                               // 3: backend.proto.offering.v1.PriceUnit
	(Service_Status)(0),                          // 4: backend.proto.offering.v1.Service.Status
	(Service_Type)(0),                            // 5: backend.proto.offering.v1.Service.Type
	(ResultValidityPeriod_ValidityPeriodType)(0), // 6: backend.proto.offering.v1.ResultValidityPeriod.ValidityPeriodType
	(ValidityDuration_DurationUnit)(0),           // 7: backend.proto.offering.v1.ValidityDuration.DurationUnit
	(ConditionalDefaultService_DateType)(0),      // 8: backend.proto.offering.v1.ConditionalDefaultService.DateType
	(ConditionalDefaultService_StayUnit)(0),      // 9: backend.proto.offering.v1.ConditionalDefaultService.StayUnit
	(*Service)(nil),                              // 10: backend.proto.offering.v1.Service
	(*AdditionalService)(nil),                    // 11: backend.proto.offering.v1.AdditionalService
	(*AutoRollover)(nil),                         // 12: backend.proto.offering.v1.AutoRollover
	(*AvailableBusiness)(nil),                    // 13: backend.proto.offering.v1.AvailableBusiness
	(*AvailableStaff)(nil),                       // 14: backend.proto.offering.v1.AvailableStaff
	(*AvailableLodgingType)(nil),                 // 15: backend.proto.offering.v1.AvailableLodgingType
	(*ServiceAttributes)(nil),                    // 16: backend.proto.offering.v1.ServiceAttributes
	(*PrerequisiteRule)(nil),                     // 17: backend.proto.offering.v1.PrerequisiteRule
	(*ResultValidityPeriod)(nil),                 // 18: backend.proto.offering.v1.ResultValidityPeriod
	(*ValidityDuration)(nil),                     // 19: backend.proto.offering.v1.ValidityDuration
	(*AvailablePetTypeBreed)(nil),                // 20: backend.proto.offering.v1.AvailablePetTypeBreed
	(*AvailablePetType)(nil),                     // 21: backend.proto.offering.v1.AvailablePetType
	(*AvailablePetSize)(nil),                     // 22: backend.proto.offering.v1.AvailablePetSize
	(*AvailableCoatType)(nil),                    // 23: backend.proto.offering.v1.AvailableCoatType
	(*AvailablePetCode)(nil),                     // 24: backend.proto.offering.v1.AvailablePetCode
	(*AvailablePetWeight)(nil),                   // 25: backend.proto.offering.v1.AvailablePetWeight
	(*PetWeightRange)(nil),                       // 26: backend.proto.offering.v1.PetWeightRange
	(*DefaultService)(nil),                       // 27: backend.proto.offering.v1.DefaultService
	(*ConditionalDefaultService)(nil),            // 28: backend.proto.offering.v1.ConditionalDefaultService
	(*BusinessStaffOverride)(nil),                // 29: backend.proto.offering.v1.BusinessStaffOverride
	(*BusinessOverride)(nil),                     // 30: backend.proto.offering.v1.BusinessOverride
	(*StaffOverride)(nil),                        // 31: backend.proto.offering.v1.StaffOverride
	(*OverrideValues)(nil),                       // 32: backend.proto.offering.v1.OverrideValues
	(*PetOverride)(nil),                          // 33: backend.proto.offering.v1.PetOverride
	(*CustomizedService)(nil),                    // 34: backend.proto.offering.v1.CustomizedService
	(*ConditionalDefaultService_Condition)(nil),  // 35: backend.proto.offering.v1.ConditionalDefaultService.Condition
	(*ConditionalDefaultService_ApplyRule)(nil),  // 36: backend.proto.offering.v1.ConditionalDefaultService.ApplyRule
	(v1.OrganizationType)(0),                     // 37: backend.proto.organization.v1.OrganizationType
	(OfferingSource)(0),                          // 38: backend.proto.offering.v1.OfferingSource
	(*money.Money)(nil),                          // 39: google.type.Money
	(*timestamppb.Timestamp)(nil),                // 40: google.protobuf.Timestamp
}
var file_backend_proto_offering_v1_service_proto_depIdxs = []int32{
	37, // 0: backend.proto.offering.v1.Service.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	38, // 1: backend.proto.offering.v1.Service.source:type_name -> backend.proto.offering.v1.OfferingSource
	4,  // 2: backend.proto.offering.v1.Service.status:type_name -> backend.proto.offering.v1.Service.Status
	5,  // 3: backend.proto.offering.v1.Service.type:type_name -> backend.proto.offering.v1.Service.Type
	39, // 4: backend.proto.offering.v1.Service.price:type_name -> google.type.Money
	40, // 5: backend.proto.offering.v1.Service.create_time:type_name -> google.protobuf.Timestamp
	40, // 6: backend.proto.offering.v1.Service.update_time:type_name -> google.protobuf.Timestamp
	40, // 7: backend.proto.offering.v1.Service.delete_time:type_name -> google.protobuf.Timestamp
	13, // 8: backend.proto.offering.v1.Service.available_business:type_name -> backend.proto.offering.v1.AvailableBusiness
	11, // 9: backend.proto.offering.v1.Service.additional_service:type_name -> backend.proto.offering.v1.AdditionalService
	20, // 10: backend.proto.offering.v1.Service.available_type_breed:type_name -> backend.proto.offering.v1.AvailablePetTypeBreed
	22, // 11: backend.proto.offering.v1.Service.available_pet_size:type_name -> backend.proto.offering.v1.AvailablePetSize
	23, // 12: backend.proto.offering.v1.Service.available_coat_type:type_name -> backend.proto.offering.v1.AvailableCoatType
	24, // 13: backend.proto.offering.v1.Service.available_pet_code:type_name -> backend.proto.offering.v1.AvailablePetCode
	25, // 14: backend.proto.offering.v1.Service.available_pet_weight:type_name -> backend.proto.offering.v1.AvailablePetWeight
	29, // 15: backend.proto.offering.v1.Service.business_staff_overrides:type_name -> backend.proto.offering.v1.BusinessStaffOverride
	33, // 16: backend.proto.offering.v1.Service.pet_overrides:type_name -> backend.proto.offering.v1.PetOverride
	16, // 17: backend.proto.offering.v1.Service.attributes:type_name -> backend.proto.offering.v1.ServiceAttributes
	12, // 18: backend.proto.offering.v1.ServiceAttributes.auto_rollover:type_name -> backend.proto.offering.v1.AutoRollover
	14, // 19: backend.proto.offering.v1.ServiceAttributes.available_staff:type_name -> backend.proto.offering.v1.AvailableStaff
	15, // 20: backend.proto.offering.v1.ServiceAttributes.available_lodging_type:type_name -> backend.proto.offering.v1.AvailableLodgingType
	18, // 21: backend.proto.offering.v1.ServiceAttributes.result_validity_period:type_name -> backend.proto.offering.v1.ResultValidityPeriod
	17, // 22: backend.proto.offering.v1.ServiceAttributes.prerequisite_rule:type_name -> backend.proto.offering.v1.PrerequisiteRule
	3,  // 23: backend.proto.offering.v1.ServiceAttributes.price_unit:type_name -> backend.proto.offering.v1.PriceUnit
	27, // 24: backend.proto.offering.v1.ServiceAttributes.default_service:type_name -> backend.proto.offering.v1.DefaultService
	28, // 25: backend.proto.offering.v1.ServiceAttributes.conditional_default_service:type_name -> backend.proto.offering.v1.ConditionalDefaultService
	6,  // 26: backend.proto.offering.v1.ResultValidityPeriod.period_type:type_name -> backend.proto.offering.v1.ResultValidityPeriod.ValidityPeriodType
	19, // 27: backend.proto.offering.v1.ResultValidityPeriod.duration:type_name -> backend.proto.offering.v1.ValidityDuration
	7,  // 28: backend.proto.offering.v1.ValidityDuration.unit:type_name -> backend.proto.offering.v1.ValidityDuration.DurationUnit
	21, // 29: backend.proto.offering.v1.AvailablePetTypeBreed.available_pet_types:type_name -> backend.proto.offering.v1.AvailablePetType
	2,  // 30: backend.proto.offering.v1.AvailablePetCode.rule_type:type_name -> backend.proto.offering.v1.AvailabilityRuleType
	26, // 31: backend.proto.offering.v1.AvailablePetWeight.pet_weight_ranges:type_name -> backend.proto.offering.v1.PetWeightRange
	35, // 32: backend.proto.offering.v1.ConditionalDefaultService.condition:type_name -> backend.proto.offering.v1.ConditionalDefaultService.Condition
	36, // 33: backend.proto.offering.v1.ConditionalDefaultService.apply_rules:type_name -> backend.proto.offering.v1.ConditionalDefaultService.ApplyRule
	30, // 34: backend.proto.offering.v1.BusinessStaffOverride.business_override:type_name -> backend.proto.offering.v1.BusinessOverride
	31, // 35: backend.proto.offering.v1.BusinessStaffOverride.staff_overrides:type_name -> backend.proto.offering.v1.StaffOverride
	39, // 36: backend.proto.offering.v1.BusinessOverride.price:type_name -> google.type.Money
	39, // 37: backend.proto.offering.v1.StaffOverride.price:type_name -> google.type.Money
	39, // 38: backend.proto.offering.v1.OverrideValues.price:type_name -> google.type.Money
	39, // 39: backend.proto.offering.v1.PetOverride.price:type_name -> google.type.Money
	10, // 40: backend.proto.offering.v1.CustomizedService.service:type_name -> backend.proto.offering.v1.Service
	39, // 41: backend.proto.offering.v1.CustomizedService.price:type_name -> google.type.Money
	0,  // 42: backend.proto.offering.v1.CustomizedService.price_override_type:type_name -> backend.proto.offering.v1.OverrideType
	0,  // 43: backend.proto.offering.v1.CustomizedService.duration_override_type:type_name -> backend.proto.offering.v1.OverrideType
	9,  // 44: backend.proto.offering.v1.ConditionalDefaultService.Condition.unit:type_name -> backend.proto.offering.v1.ConditionalDefaultService.StayUnit
	8,  // 45: backend.proto.offering.v1.ConditionalDefaultService.ApplyRule.date_type:type_name -> backend.proto.offering.v1.ConditionalDefaultService.DateType
	46, // [46:46] is the sub-list for method output_type
	46, // [46:46] is the sub-list for method input_type
	46, // [46:46] is the sub-list for extension type_name
	46, // [46:46] is the sub-list for extension extendee
	0,  // [0:46] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_service_proto_init() }
func file_backend_proto_offering_v1_service_proto_init() {
	if File_backend_proto_offering_v1_service_proto != nil {
		return
	}
	file_backend_proto_offering_v1_common_proto_init()
	file_backend_proto_offering_v1_service_ob_setting_proto_init()
	file_backend_proto_offering_v1_service_proto_msgTypes[0].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_proto_msgTypes[2].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_proto_msgTypes[6].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_proto_msgTypes[7].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_proto_msgTypes[8].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_proto_msgTypes[20].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_proto_msgTypes[21].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_proto_msgTypes[22].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_proto_msgTypes[23].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_proto_msgTypes[24].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_proto_msgTypes[26].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_proto_rawDesc), len(file_backend_proto_offering_v1_service_proto_rawDesc)),
			NumEnums:      10,
			NumMessages:   27,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_offering_v1_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_service_proto_depIdxs,
		EnumInfos:         file_backend_proto_offering_v1_service_proto_enumTypes,
		MessageInfos:      file_backend_proto_offering_v1_service_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_service_proto = out.File
	file_backend_proto_offering_v1_service_proto_goTypes = nil
	file_backend_proto_offering_v1_service_proto_depIdxs = nil
}
