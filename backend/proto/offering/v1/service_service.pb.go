// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.10
// 	protoc        (unknown)
// source: backend/proto/offering/v1/service_service.proto

package offeringpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	v1 "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 删除服务请求
type DeleteServiceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务ID
	ServiceId     int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteServiceRequest) Reset() {
	*x = DeleteServiceRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceRequest) ProtoMessage() {}

func (x *DeleteServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceRequest.ProtoReflect.Descriptor instead.
func (*DeleteServiceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{0}
}

func (x *DeleteServiceRequest) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

// 删除服务响应
type DeleteServiceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteServiceResponse) Reset() {
	*x = DeleteServiceResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceResponse) ProtoMessage() {}

func (x *DeleteServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceResponse.ProtoReflect.Descriptor instead.
func (*DeleteServiceResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{1}
}

// 更新服务请求
type UpdateServiceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务更新配置
	Service       *ServiceUpdateDef `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateServiceRequest) Reset() {
	*x = UpdateServiceRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceRequest) ProtoMessage() {}

func (x *UpdateServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceRequest.ProtoReflect.Descriptor instead.
func (*UpdateServiceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateServiceRequest) GetService() *ServiceUpdateDef {
	if x != nil {
		return x.Service
	}
	return nil
}

// 更新服务响应
type UpdateServiceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateServiceResponse) Reset() {
	*x = UpdateServiceResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceResponse) ProtoMessage() {}

func (x *UpdateServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceResponse.ProtoReflect.Descriptor instead.
func (*UpdateServiceResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{3}
}

// (-- api-linter: core::0133::request-resource-field=disabled
//
//	aip.dev/not-precedent: 不复用 model 结构，单独定义 ServiceCreateDef 结构 --)
//
// 创建服务请求
type CreateServiceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务创建配置
	Service       *ServiceCreateDef `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateServiceRequest) Reset() {
	*x = CreateServiceRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceRequest) ProtoMessage() {}

func (x *CreateServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceRequest.ProtoReflect.Descriptor instead.
func (*CreateServiceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{4}
}

func (x *CreateServiceRequest) GetService() *ServiceCreateDef {
	if x != nil {
		return x.Service
	}
	return nil
}

// 创建服务响应
type CreateServiceResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务 ID
	ServiceId     int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateServiceResponse) Reset() {
	*x = CreateServiceResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceResponse) ProtoMessage() {}

func (x *CreateServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceResponse.ProtoReflect.Descriptor instead.
func (*CreateServiceResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{5}
}

func (x *CreateServiceResponse) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

// 获取服务请求
type GetServiceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务ID
	ServiceId     int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceRequest) Reset() {
	*x = GetServiceRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceRequest) ProtoMessage() {}

func (x *GetServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceRequest.ProtoReflect.Descriptor instead.
func (*GetServiceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetServiceRequest) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

// 获取服务响应
type GetServiceResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务
	Service       *Service `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceResponse) Reset() {
	*x = GetServiceResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceResponse) ProtoMessage() {}

func (x *GetServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceResponse.ProtoReflect.Descriptor instead.
func (*GetServiceResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetServiceResponse) GetService() *Service {
	if x != nil {
		return x.Service
	}
	return nil
}

// 分类服务
type CategoryService struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分类ID
	CategoryId int64 `protobuf:"varint,1,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// 分类名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 服务模板列表
	Services      []*Service `protobuf:"bytes,3,rep,name=services,proto3" json:"services,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CategoryService) Reset() {
	*x = CategoryService{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CategoryService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CategoryService) ProtoMessage() {}

func (x *CategoryService) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CategoryService.ProtoReflect.Descriptor instead.
func (*CategoryService) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{8}
}

func (x *CategoryService) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *CategoryService) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CategoryService) GetServices() []*Service {
	if x != nil {
		return x.Services
	}
	return nil
}

// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: 无 parent 语义 --)
//
// list ob services request
type ListOBServicesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The business_id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// filter
	Filter *ListOBServicesRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	// 分页信息
	Pagination    *PaginationRef `protobuf:"bytes,9,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListOBServicesRequest) Reset() {
	*x = ListOBServicesRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOBServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOBServicesRequest) ProtoMessage() {}

func (x *ListOBServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOBServicesRequest.ProtoReflect.Descriptor instead.
func (*ListOBServicesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{9}
}

func (x *ListOBServicesRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListOBServicesRequest) GetFilter() *ListOBServicesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListOBServicesRequest) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0132::response-unknown-fields=disabled
//
//	aip.dev/not-precedent: 额外返回 total. --)
//
// list ob services response
type ListOBServicesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ob setting
	ServiceObSettings []*ServiceOBSetting `protobuf:"bytes,1,rep,name=service_ob_settings,json=serviceObSettings,proto3" json:"service_ob_settings,omitempty"`
	// 分页信息
	Pagination *PaginationRef `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// 总数
	Total         int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListOBServicesResponse) Reset() {
	*x = ListOBServicesResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOBServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOBServicesResponse) ProtoMessage() {}

func (x *ListOBServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOBServicesResponse.ProtoReflect.Descriptor instead.
func (*ListOBServicesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{10}
}

func (x *ListOBServicesResponse) GetServiceObSettings() []*ServiceOBSetting {
	if x != nil {
		return x.ServiceObSettings
	}
	return nil
}

func (x *ListOBServicesResponse) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListOBServicesResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: 无 parent 语义 --)
//
// list services request
type ListServicesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The organization type
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// The organization ID.
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// filter
	Filter *ListServicesRequest_Filter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
	// 分页信息
	Pagination    *PaginationRef `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListServicesRequest) Reset() {
	*x = ListServicesRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServicesRequest) ProtoMessage() {}

func (x *ListServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServicesRequest.ProtoReflect.Descriptor instead.
func (*ListServicesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{11}
}

func (x *ListServicesRequest) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *ListServicesRequest) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *ListServicesRequest) GetFilter() *ListServicesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListServicesRequest) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0132::response-unknown-fields=disabled
//
//	aip.dev/not-precedent: 额外返回 total. --)
//
// list setting services response
type ListServicesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务模板列表
	Services []*Service `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	// 分页信息
	Pagination *PaginationRef `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// 总数
	Total         int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListServicesResponse) Reset() {
	*x = ListServicesResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServicesResponse) ProtoMessage() {}

func (x *ListServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServicesResponse.ProtoReflect.Descriptor instead.
func (*ListServicesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{12}
}

func (x *ListServicesResponse) GetServices() []*Service {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *ListServicesResponse) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListServicesResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// (-- api-linter: core::0234::request-unknown-fields=disabled
//
//	aip.dev/not-precedent: 需要租户信息 --)
//
// (-- api-linter: core::0234::request-requests-field=disabled
//
//	aip.dev/not-precedent: 不复用 UpdateServiceRequest 结构 --)
//
// (-- api-linter: core::0234::request-parent-field=disabled
//
//	aip.dev/not-precedent: 无 parent 语义 --)
//
// 批量更新服务请求
type BatchUpdateServicesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 当前的租户类型
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// 当前的租户 ID
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// 服务更新配置列表
	UpdateServices []*ServiceUpdateDef `protobuf:"bytes,3,rep,name=update_services,json=updateServices,proto3" json:"update_services,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BatchUpdateServicesRequest) Reset() {
	*x = BatchUpdateServicesRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchUpdateServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateServicesRequest) ProtoMessage() {}

func (x *BatchUpdateServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateServicesRequest.ProtoReflect.Descriptor instead.
func (*BatchUpdateServicesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{13}
}

func (x *BatchUpdateServicesRequest) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *BatchUpdateServicesRequest) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *BatchUpdateServicesRequest) GetUpdateServices() []*ServiceUpdateDef {
	if x != nil {
		return x.UpdateServices
	}
	return nil
}

// 单个服务的更新配置
type ServiceUpdateDef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务 ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 分类 ID（可选）
	CategoryId *int64 `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3,oneof" json:"category_id,omitempty"`
	// 服务名称（可选）
	Name *string `protobuf:"bytes,3,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// 描述
	Description *string `protobuf:"bytes,4,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// 颜色
	ColorCode *string `protobuf:"bytes,5,opt,name=color_code,json=colorCode,proto3,oneof" json:"color_code,omitempty"`
	// 排序值（可选）
	Sort *int64 `protobuf:"varint,6,opt,name=sort,proto3,oneof" json:"sort,omitempty"`
	// 图片
	Images []string `protobuf:"bytes,7,rep,name=images,proto3" json:"images,omitempty"`
	// 状态
	Status *Service_Status `protobuf:"varint,8,opt,name=status,proto3,enum=backend.proto.offering.v1.Service_Status,oneof" json:"status,omitempty"`
	// 可用 business scope
	AvailableBusiness *AvailableBusiness `protobuf:"bytes,9,opt,name=available_business,json=availableBusiness,proto3,oneof" json:"available_business,omitempty"`
	// 关联的属性值
	Attributes *ServiceAttributes `protobuf:"bytes,10,opt,name=attributes,proto3,oneof" json:"attributes,omitempty"`
	// 附加服务配置（可选）
	AdditionalService *AdditionalService `protobuf:"bytes,11,opt,name=additional_service,json=additionalService,proto3,oneof" json:"additional_service,omitempty"`
	// 可用宠物类型和品种配置（可选）
	AvailableTypeBreed *AvailablePetTypeBreed `protobuf:"bytes,12,opt,name=available_type_breed,json=availableTypeBreed,proto3,oneof" json:"available_type_breed,omitempty"`
	// 可用宠物尺寸配置（可选）
	AvailablePetSize *AvailablePetSize `protobuf:"bytes,13,opt,name=available_pet_size,json=availablePetSize,proto3,oneof" json:"available_pet_size,omitempty"`
	// 可用宠物毛类型配置（可选）
	AvailableCoatType *AvailableCoatType `protobuf:"bytes,14,opt,name=available_coat_type,json=availableCoatType,proto3,oneof" json:"available_coat_type,omitempty"`
	// 可用宠物代码配置（可选）
	AvailablePetCode *AvailablePetCode `protobuf:"bytes,15,opt,name=available_pet_code,json=availablePetCode,proto3,oneof" json:"available_pet_code,omitempty"`
	// 可用宠物体重配置（可选）
	AvailablePetWeight *AvailablePetWeight `protobuf:"bytes,16,opt,name=available_pet_weight,json=availablePetWeight,proto3,oneof" json:"available_pet_weight,omitempty"`
	// 价格配置
	Price *money.Money `protobuf:"bytes,17,opt,name=price,proto3,oneof" json:"price,omitempty"`
	// 税收 ID
	TaxId *int64 `protobuf:"varint,19,opt,name=tax_id,json=taxId,proto3,oneof" json:"tax_id,omitempty"`
	// Business and staff override configuration (price, duration, tax_id overrides for specific businesses and staff)
	BusinessStaffOverrides []*BusinessStaffOverride `protobuf:"bytes,20,rep,name=business_staff_overrides,json=businessStaffOverrides,proto3" json:"business_staff_overrides,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *ServiceUpdateDef) Reset() {
	*x = ServiceUpdateDef{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceUpdateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceUpdateDef) ProtoMessage() {}

func (x *ServiceUpdateDef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceUpdateDef.ProtoReflect.Descriptor instead.
func (*ServiceUpdateDef) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{14}
}

func (x *ServiceUpdateDef) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceUpdateDef) GetCategoryId() int64 {
	if x != nil && x.CategoryId != nil {
		return *x.CategoryId
	}
	return 0
}

func (x *ServiceUpdateDef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *ServiceUpdateDef) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *ServiceUpdateDef) GetColorCode() string {
	if x != nil && x.ColorCode != nil {
		return *x.ColorCode
	}
	return ""
}

func (x *ServiceUpdateDef) GetSort() int64 {
	if x != nil && x.Sort != nil {
		return *x.Sort
	}
	return 0
}

func (x *ServiceUpdateDef) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *ServiceUpdateDef) GetStatus() Service_Status {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return Service_STATUS_UNSPECIFIED
}

func (x *ServiceUpdateDef) GetAvailableBusiness() *AvailableBusiness {
	if x != nil {
		return x.AvailableBusiness
	}
	return nil
}

func (x *ServiceUpdateDef) GetAttributes() *ServiceAttributes {
	if x != nil {
		return x.Attributes
	}
	return nil
}

func (x *ServiceUpdateDef) GetAdditionalService() *AdditionalService {
	if x != nil {
		return x.AdditionalService
	}
	return nil
}

func (x *ServiceUpdateDef) GetAvailableTypeBreed() *AvailablePetTypeBreed {
	if x != nil {
		return x.AvailableTypeBreed
	}
	return nil
}

func (x *ServiceUpdateDef) GetAvailablePetSize() *AvailablePetSize {
	if x != nil {
		return x.AvailablePetSize
	}
	return nil
}

func (x *ServiceUpdateDef) GetAvailableCoatType() *AvailableCoatType {
	if x != nil {
		return x.AvailableCoatType
	}
	return nil
}

func (x *ServiceUpdateDef) GetAvailablePetCode() *AvailablePetCode {
	if x != nil {
		return x.AvailablePetCode
	}
	return nil
}

func (x *ServiceUpdateDef) GetAvailablePetWeight() *AvailablePetWeight {
	if x != nil {
		return x.AvailablePetWeight
	}
	return nil
}

func (x *ServiceUpdateDef) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *ServiceUpdateDef) GetTaxId() int64 {
	if x != nil && x.TaxId != nil {
		return *x.TaxId
	}
	return 0
}

func (x *ServiceUpdateDef) GetBusinessStaffOverrides() []*BusinessStaffOverride {
	if x != nil {
		return x.BusinessStaffOverrides
	}
	return nil
}

// (-- api-linter: core::0234::response-resource-field=disabled
//
//	aip.dev/not-precedent: 不返回 Service 类型 --)
//
// 批量更新服务响应
type BatchUpdateServicesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchUpdateServicesResponse) Reset() {
	*x = BatchUpdateServicesResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchUpdateServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateServicesResponse) ProtoMessage() {}

func (x *BatchUpdateServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateServicesResponse.ProtoReflect.Descriptor instead.
func (*BatchUpdateServicesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{15}
}

// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: 无 parent 语义 --)
//
// 查询可用服务请求（用于预约场景）
type ListAvailableServicesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 当前的租户类型，如果是 Business 会包含 override 信息
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// 当前的租户 ID
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// 包含的可用性上下文，比如选择 Business/Pet/Staff/Lodging/Service 等
	Context *ListAvailableServicesRequest_AvailabilityContext `protobuf:"bytes,3,opt,name=context,proto3" json:"context,omitempty"`
	// 可选的过滤条件
	Filter *ListAvailableServicesRequest_Filter `protobuf:"bytes,5,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	// 分页信息
	Pagination    *PaginationRef `protobuf:"bytes,6,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAvailableServicesRequest) Reset() {
	*x = ListAvailableServicesRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAvailableServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAvailableServicesRequest) ProtoMessage() {}

func (x *ListAvailableServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAvailableServicesRequest.ProtoReflect.Descriptor instead.
func (*ListAvailableServicesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{16}
}

func (x *ListAvailableServicesRequest) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *ListAvailableServicesRequest) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *ListAvailableServicesRequest) GetContext() *ListAvailableServicesRequest_AvailabilityContext {
	if x != nil {
		return x.Context
	}
	return nil
}

func (x *ListAvailableServicesRequest) GetFilter() *ListAvailableServicesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListAvailableServicesRequest) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0132::response-unknown-fields=disabled
//
//	aip.dev/not-precedent: 额外返回 total. --)
//
// 查询可用服务响应
type ListAvailableServicesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 可用服务列表
	Services []*AvailableService `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	// 分页信息
	Pagination *PaginationRef `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// 总数
	Total         int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAvailableServicesResponse) Reset() {
	*x = ListAvailableServicesResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAvailableServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAvailableServicesResponse) ProtoMessage() {}

func (x *ListAvailableServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAvailableServicesResponse.ProtoReflect.Descriptor instead.
func (*ListAvailableServicesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{17}
}

func (x *ListAvailableServicesResponse) GetServices() []*AvailableService {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *ListAvailableServicesResponse) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListAvailableServicesResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// AvailableService 用于 ListAvailableServices 的返回结构
// 只包含前端消费所需的信息，过滤掉纯后端配置信息
type AvailableService struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 主键 ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 护理类型 ID
	CareTypeId int64 `protobuf:"varint,2,opt,name=care_type_id,json=careTypeId,proto3" json:"care_type_id,omitempty"`
	// 分类 ID
	CategoryId int64 `protobuf:"varint,3,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// 服务名称
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// 服务描述
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// 服务颜色
	ColorCode string `protobuf:"bytes,6,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// 排序值
	Sort int64 `protobuf:"varint,7,opt,name=sort,proto3" json:"sort,omitempty"`
	// 图片
	Images []string `protobuf:"bytes,8,rep,name=images,proto3" json:"images,omitempty"`
	// 来源
	Source OfferingSource `protobuf:"varint,9,opt,name=source,proto3,enum=backend.proto.offering.v1.OfferingSource" json:"source,omitempty"`
	// 状态
	Status Service_Status `protobuf:"varint,10,opt,name=status,proto3,enum=backend.proto.offering.v1.Service_Status" json:"status,omitempty"`
	// 价格
	Price *money.Money `protobuf:"bytes,11,opt,name=price,proto3" json:"price,omitempty"`
	// 税收 ID
	TaxId int64 `protobuf:"varint,12,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// 服务属性
	Attributes *AvailableServiceAttributes `protobuf:"bytes,13,opt,name=attributes,proto3,oneof" json:"attributes,omitempty"`
	// 企业员工覆盖信息
	BusinessStaffOverrides []*BusinessStaffOverride `protobuf:"bytes,14,rep,name=business_staff_overrides,json=businessStaffOverrides,proto3" json:"business_staff_overrides,omitempty"`
	// 宠物覆盖信息
	PetOverrides  []*PetOverride `protobuf:"bytes,15,rep,name=pet_overrides,json=petOverrides,proto3" json:"pet_overrides,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AvailableService) Reset() {
	*x = AvailableService{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailableService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailableService) ProtoMessage() {}

func (x *AvailableService) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailableService.ProtoReflect.Descriptor instead.
func (*AvailableService) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{18}
}

func (x *AvailableService) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AvailableService) GetCareTypeId() int64 {
	if x != nil {
		return x.CareTypeId
	}
	return 0
}

func (x *AvailableService) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *AvailableService) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AvailableService) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AvailableService) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *AvailableService) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *AvailableService) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *AvailableService) GetSource() OfferingSource {
	if x != nil {
		return x.Source
	}
	return OfferingSource_OFFERING_SOURCE_UNSPECIFIED
}

func (x *AvailableService) GetStatus() Service_Status {
	if x != nil {
		return x.Status
	}
	return Service_STATUS_UNSPECIFIED
}

func (x *AvailableService) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *AvailableService) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *AvailableService) GetAttributes() *AvailableServiceAttributes {
	if x != nil {
		return x.Attributes
	}
	return nil
}

func (x *AvailableService) GetBusinessStaffOverrides() []*BusinessStaffOverride {
	if x != nil {
		return x.BusinessStaffOverrides
	}
	return nil
}

func (x *AvailableService) GetPetOverrides() []*PetOverride {
	if x != nil {
		return x.PetOverrides
	}
	return nil
}

// 可用服务属性（只包含前端需要的，大部分是静态显示内容，少部分是前端消费的配置）
type AvailableServiceAttributes struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务时长，常见于 GROOMING, DOG_WALKING 服务
	Duration *int32 `protobuf:"varint,1,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	// 最大服务时长，常见于 DAYCARE 服务
	MaxDuration *int32 `protobuf:"varint,2,opt,name=max_duration,json=maxDuration,proto3,oneof" json:"max_duration,omitempty"`
	// 是否需要员工，常见于 ADD_ON 类型服务
	IsRequiredStaff *bool `protobuf:"varint,3,opt,name=is_required_staff,json=isRequiredStaff,proto3,oneof" json:"is_required_staff,omitempty"`
	// 在线预约别名，常见于 EVALUATION 服务
	ObAlias *string `protobuf:"bytes,4,opt,name=ob_alias,json=obAlias,proto3,oneof" json:"ob_alias,omitempty"`
	// 价格单位，常见于 BOARDING 服务
	PriceUnit     *PriceUnit `protobuf:"varint,5,opt,name=price_unit,json=priceUnit,proto3,enum=backend.proto.offering.v1.PriceUnit,oneof" json:"price_unit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AvailableServiceAttributes) Reset() {
	*x = AvailableServiceAttributes{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailableServiceAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailableServiceAttributes) ProtoMessage() {}

func (x *AvailableServiceAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailableServiceAttributes.ProtoReflect.Descriptor instead.
func (*AvailableServiceAttributes) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{19}
}

func (x *AvailableServiceAttributes) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

func (x *AvailableServiceAttributes) GetMaxDuration() int32 {
	if x != nil && x.MaxDuration != nil {
		return *x.MaxDuration
	}
	return 0
}

func (x *AvailableServiceAttributes) GetIsRequiredStaff() bool {
	if x != nil && x.IsRequiredStaff != nil {
		return *x.IsRequiredStaff
	}
	return false
}

func (x *AvailableServiceAttributes) GetObAlias() string {
	if x != nil && x.ObAlias != nil {
		return *x.ObAlias
	}
	return ""
}

func (x *AvailableServiceAttributes) GetPriceUnit() PriceUnit {
	if x != nil && x.PriceUnit != nil {
		return *x.PriceUnit
	}
	return PriceUnit_PRICE_UNIT_UNSPECIFIED
}

// UpdateOBServiceRequest
type UpdateOBServiceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The business_id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 服务
	ServiceId int64 `protobuf:"varint,3,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// Whether the service is available for online booking
	IsAvailable *bool `protobuf:"varint,11,opt,name=is_available,json=isAvailable,proto3,oneof" json:"is_available,omitempty"`
	// Display price mode: 0 - Do not show, 1 - Show fixed price, 2 - Show "starting at", 3 - Show "varies"
	ShowBasePrice *ServiceOBSetting_ShowBasePriceMode `protobuf:"varint,12,opt,name=show_base_price,json=showBasePrice,proto3,enum=backend.proto.offering.v1.ServiceOBSetting_ShowBasePriceMode,oneof" json:"show_base_price,omitempty"`
	// Whether all staff are available for this service when booking online
	IsAllStaff *bool `protobuf:"varint,13,opt,name=is_all_staff,json=isAllStaff,proto3,oneof" json:"is_all_staff,omitempty"`
	// staff ids
	StaffIds []int64 `protobuf:"varint,14,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
	// Display duration mode: 0 - Do not show, 1 - Show duration
	ShowDuration *ServiceOBSetting_ShowDuration `protobuf:"varint,15,opt,name=show_duration,json=showDuration,proto3,enum=backend.proto.offering.v1.ServiceOBSetting_ShowDuration,oneof" json:"show_duration,omitempty"`
	// Whether this service can be booked together with other care types in one appointment
	AllowGroupBooking *bool `protobuf:"varint,16,opt,name=allow_group_booking,json=allowGroupBooking,proto3,oneof" json:"allow_group_booking,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *UpdateOBServiceRequest) Reset() {
	*x = UpdateOBServiceRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateOBServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOBServiceRequest) ProtoMessage() {}

func (x *UpdateOBServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOBServiceRequest.ProtoReflect.Descriptor instead.
func (*UpdateOBServiceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{20}
}

func (x *UpdateOBServiceRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateOBServiceRequest) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *UpdateOBServiceRequest) GetIsAvailable() bool {
	if x != nil && x.IsAvailable != nil {
		return *x.IsAvailable
	}
	return false
}

func (x *UpdateOBServiceRequest) GetShowBasePrice() ServiceOBSetting_ShowBasePriceMode {
	if x != nil && x.ShowBasePrice != nil {
		return *x.ShowBasePrice
	}
	return ServiceOBSetting_SHOW_BASE_PRICE_MODE_DO_NOT_SHOW
}

func (x *UpdateOBServiceRequest) GetIsAllStaff() bool {
	if x != nil && x.IsAllStaff != nil {
		return *x.IsAllStaff
	}
	return false
}

func (x *UpdateOBServiceRequest) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

func (x *UpdateOBServiceRequest) GetShowDuration() ServiceOBSetting_ShowDuration {
	if x != nil && x.ShowDuration != nil {
		return *x.ShowDuration
	}
	return ServiceOBSetting_SHOW_DURATION_NO
}

func (x *UpdateOBServiceRequest) GetAllowGroupBooking() bool {
	if x != nil && x.AllowGroupBooking != nil {
		return *x.AllowGroupBooking
	}
	return false
}

// Update OB Service Response
type UpdateOBServiceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateOBServiceResponse) Reset() {
	*x = UpdateOBServiceResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateOBServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOBServiceResponse) ProtoMessage() {}

func (x *UpdateOBServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOBServiceResponse.ProtoReflect.Descriptor instead.
func (*UpdateOBServiceResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{21}
}

// (-- api-linter: core::0231::request-unknown-fields=disabled
//
//	aip.dev/not-precedent: 直接根据 ID 批量获取服务 --)
//
// (-- api-linter: core::0231::request-names-field=disabled
//
//	aip.dev/not-precedent: 直接根据 ID 批量获取服务 --)
//
// 批量获取服务请求
type BatchGetServicesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 当前的租户类型
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// 当前的租户 ID
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// 服务ID列表
	Ids           []int64 `protobuf:"varint,3,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchGetServicesRequest) Reset() {
	*x = BatchGetServicesRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetServicesRequest) ProtoMessage() {}

func (x *BatchGetServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetServicesRequest.ProtoReflect.Descriptor instead.
func (*BatchGetServicesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{22}
}

func (x *BatchGetServicesRequest) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *BatchGetServicesRequest) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *BatchGetServicesRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// 批量获取服务响应
type BatchGetServicesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务列表
	Services      []*Service `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchGetServicesResponse) Reset() {
	*x = BatchGetServicesResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetServicesResponse) ProtoMessage() {}

func (x *BatchGetServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetServicesResponse.ProtoReflect.Descriptor instead.
func (*BatchGetServicesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{23}
}

func (x *BatchGetServicesResponse) GetServices() []*Service {
	if x != nil {
		return x.Services
	}
	return nil
}

// Service 创建配置定义
type ServiceCreateDef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 组织类型
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// 组织 ID
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// 护理类型 ID
	CareTypeId int64 `protobuf:"varint,3,opt,name=care_type_id,json=careTypeId,proto3" json:"care_type_id,omitempty"`
	// 分类 ID（可选）
	CategoryId *int64 `protobuf:"varint,4,opt,name=category_id,json=categoryId,proto3,oneof" json:"category_id,omitempty"`
	// 服务名称
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	// 描述（可选）
	Description *string `protobuf:"bytes,6,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// 颜色代码
	ColorCode string `protobuf:"bytes,7,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// 图片列表（可选）
	Images []string `protobuf:"bytes,9,rep,name=images,proto3" json:"images,omitempty"`
	// 来源
	Source OfferingSource `protobuf:"varint,10,opt,name=source,proto3,enum=backend.proto.offering.v1.OfferingSource" json:"source,omitempty"`
	// 状态
	Status Service_Status `protobuf:"varint,11,opt,name=status,proto3,enum=backend.proto.offering.v1.Service_Status" json:"status,omitempty"`
	// 可用业务范围
	AvailableBusiness *AvailableBusiness `protobuf:"bytes,13,opt,name=available_business,json=availableBusiness,proto3" json:"available_business,omitempty"`
	// 附加服务配置（可选）
	AdditionalService *AdditionalService `protobuf:"bytes,14,opt,name=additional_service,json=additionalService,proto3,oneof" json:"additional_service,omitempty"`
	// 可用宠物类型和品种配置（可选）
	AvailableTypeBreed *AvailablePetTypeBreed `protobuf:"bytes,15,opt,name=available_type_breed,json=availableTypeBreed,proto3,oneof" json:"available_type_breed,omitempty"`
	// 可用宠物尺寸配置（可选）
	AvailablePetSize *AvailablePetSize `protobuf:"bytes,16,opt,name=available_pet_size,json=availablePetSize,proto3,oneof" json:"available_pet_size,omitempty"`
	// 可用宠物毛类型配置（可选）
	AvailableCoatType *AvailableCoatType `protobuf:"bytes,17,opt,name=available_coat_type,json=availableCoatType,proto3,oneof" json:"available_coat_type,omitempty"`
	// 可用宠物代码配置（可选）
	AvailablePetCode *AvailablePetCode `protobuf:"bytes,18,opt,name=available_pet_code,json=availablePetCode,proto3,oneof" json:"available_pet_code,omitempty"`
	// 服务属性（可选）
	Attributes *ServiceAttributes `protobuf:"bytes,19,opt,name=attributes,proto3,oneof" json:"attributes,omitempty"`
	// 可用宠物体重配置（可选）
	AvailablePetWeight *AvailablePetWeight `protobuf:"bytes,20,opt,name=available_pet_weight,json=availablePetWeight,proto3,oneof" json:"available_pet_weight,omitempty"`
	// 价格配置
	Price *money.Money `protobuf:"bytes,21,opt,name=price,proto3" json:"price,omitempty"`
	// 税收 ID
	TaxId int64 `protobuf:"varint,23,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// Business and staff override configuration (price, duration, tax_id overrides for specific businesses and staff)
	BusinessStaffOverrides []*BusinessStaffOverride `protobuf:"bytes,25,rep,name=business_staff_overrides,json=businessStaffOverrides,proto3" json:"business_staff_overrides,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *ServiceCreateDef) Reset() {
	*x = ServiceCreateDef{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceCreateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceCreateDef) ProtoMessage() {}

func (x *ServiceCreateDef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceCreateDef.ProtoReflect.Descriptor instead.
func (*ServiceCreateDef) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{24}
}

func (x *ServiceCreateDef) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *ServiceCreateDef) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *ServiceCreateDef) GetCareTypeId() int64 {
	if x != nil {
		return x.CareTypeId
	}
	return 0
}

func (x *ServiceCreateDef) GetCategoryId() int64 {
	if x != nil && x.CategoryId != nil {
		return *x.CategoryId
	}
	return 0
}

func (x *ServiceCreateDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceCreateDef) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *ServiceCreateDef) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *ServiceCreateDef) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *ServiceCreateDef) GetSource() OfferingSource {
	if x != nil {
		return x.Source
	}
	return OfferingSource_OFFERING_SOURCE_UNSPECIFIED
}

func (x *ServiceCreateDef) GetStatus() Service_Status {
	if x != nil {
		return x.Status
	}
	return Service_STATUS_UNSPECIFIED
}

func (x *ServiceCreateDef) GetAvailableBusiness() *AvailableBusiness {
	if x != nil {
		return x.AvailableBusiness
	}
	return nil
}

func (x *ServiceCreateDef) GetAdditionalService() *AdditionalService {
	if x != nil {
		return x.AdditionalService
	}
	return nil
}

func (x *ServiceCreateDef) GetAvailableTypeBreed() *AvailablePetTypeBreed {
	if x != nil {
		return x.AvailableTypeBreed
	}
	return nil
}

func (x *ServiceCreateDef) GetAvailablePetSize() *AvailablePetSize {
	if x != nil {
		return x.AvailablePetSize
	}
	return nil
}

func (x *ServiceCreateDef) GetAvailableCoatType() *AvailableCoatType {
	if x != nil {
		return x.AvailableCoatType
	}
	return nil
}

func (x *ServiceCreateDef) GetAvailablePetCode() *AvailablePetCode {
	if x != nil {
		return x.AvailablePetCode
	}
	return nil
}

func (x *ServiceCreateDef) GetAttributes() *ServiceAttributes {
	if x != nil {
		return x.Attributes
	}
	return nil
}

func (x *ServiceCreateDef) GetAvailablePetWeight() *AvailablePetWeight {
	if x != nil {
		return x.AvailablePetWeight
	}
	return nil
}

func (x *ServiceCreateDef) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *ServiceCreateDef) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *ServiceCreateDef) GetBusinessStaffOverrides() []*BusinessStaffOverride {
	if x != nil {
		return x.BusinessStaffOverrides
	}
	return nil
}

// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: 无 parent 语义 --)
//
// ListPetOverridesRequest
type ListPetOverrideRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// pet id
	PetIds []int64 `protobuf:"varint,1,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
	// 分页信息
	Pagination    *PaginationRef `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPetOverrideRequest) Reset() {
	*x = ListPetOverrideRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPetOverrideRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetOverrideRequest) ProtoMessage() {}

func (x *ListPetOverrideRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetOverrideRequest.ProtoReflect.Descriptor instead.
func (*ListPetOverrideRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{25}
}

func (x *ListPetOverrideRequest) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

func (x *ListPetOverrideRequest) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// ListPetOverridesResponse
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0132::response-unknown-fields=disabled
//
//	aip.dev/not-precedent: 额外返回 total. --)
type ListPetOverrideResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务宠物覆盖列表
	PetOverrides []*PetOverride `protobuf:"bytes,1,rep,name=pet_overrides,json=petOverrides,proto3" json:"pet_overrides,omitempty"`
	// 分页信息
	Pagination *PaginationRef `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// 总数
	Total         int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPetOverrideResponse) Reset() {
	*x = ListPetOverrideResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPetOverrideResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetOverrideResponse) ProtoMessage() {}

func (x *ListPetOverrideResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetOverrideResponse.ProtoReflect.Descriptor instead.
func (*ListPetOverrideResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{26}
}

func (x *ListPetOverrideResponse) GetPetOverrides() []*PetOverride {
	if x != nil {
		return x.PetOverrides
	}
	return nil
}

func (x *ListPetOverrideResponse) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListPetOverrideResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// UpsertPetOverrideRequest
// (-- api-linter: core::0133::request-resource-field=disabled
//
//	aip.dev/not-precedent: 不复用 model 结构，单独定义 PetOverrideDef 结构 --)
//
// 创建服务请求
type UpsertPetOverrideRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// pet override
	PetOverride   *PetOverrideDef `protobuf:"bytes,1,opt,name=pet_override,json=petOverride,proto3" json:"pet_override,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpsertPetOverrideRequest) Reset() {
	*x = UpsertPetOverrideRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpsertPetOverrideRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertPetOverrideRequest) ProtoMessage() {}

func (x *UpsertPetOverrideRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertPetOverrideRequest.ProtoReflect.Descriptor instead.
func (*UpsertPetOverrideRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{27}
}

func (x *UpsertPetOverrideRequest) GetPetOverride() *PetOverrideDef {
	if x != nil {
		return x.PetOverride
	}
	return nil
}

// UpsertPetOverrideResponse
type UpsertPetOverrideResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务宠物覆盖列表
	PetOverride   *PetOverride `protobuf:"bytes,1,opt,name=pet_override,json=petOverride,proto3" json:"pet_override,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpsertPetOverrideResponse) Reset() {
	*x = UpsertPetOverrideResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpsertPetOverrideResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertPetOverrideResponse) ProtoMessage() {}

func (x *UpsertPetOverrideResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertPetOverrideResponse.ProtoReflect.Descriptor instead.
func (*UpsertPetOverrideResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{28}
}

func (x *UpsertPetOverrideResponse) GetPetOverride() *PetOverride {
	if x != nil {
		return x.PetOverride
	}
	return nil
}

// UpdatePetOverrideRequest
type UpdatePetOverrideRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// pet override id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet override
	PetOverride   *PetOverrideDef `protobuf:"bytes,2,opt,name=pet_override,json=petOverride,proto3" json:"pet_override,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePetOverrideRequest) Reset() {
	*x = UpdatePetOverrideRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePetOverrideRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetOverrideRequest) ProtoMessage() {}

func (x *UpdatePetOverrideRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetOverrideRequest.ProtoReflect.Descriptor instead.
func (*UpdatePetOverrideRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{29}
}

func (x *UpdatePetOverrideRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePetOverrideRequest) GetPetOverride() *PetOverrideDef {
	if x != nil {
		return x.PetOverride
	}
	return nil
}

// UpdatePetOverrideResponse
type UpdatePetOverrideResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务宠物覆盖列表
	PetOverride   *PetOverride `protobuf:"bytes,1,opt,name=pet_override,json=petOverride,proto3" json:"pet_override,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePetOverrideResponse) Reset() {
	*x = UpdatePetOverrideResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePetOverrideResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetOverrideResponse) ProtoMessage() {}

func (x *UpdatePetOverrideResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetOverrideResponse.ProtoReflect.Descriptor instead.
func (*UpdatePetOverrideResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{30}
}

func (x *UpdatePetOverrideResponse) GetPetOverride() *PetOverride {
	if x != nil {
		return x.PetOverride
	}
	return nil
}

// DeletePetOverrideRequest
type DeletePetOverrideRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// pet override id
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePetOverrideRequest) Reset() {
	*x = DeletePetOverrideRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePetOverrideRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetOverrideRequest) ProtoMessage() {}

func (x *DeletePetOverrideRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetOverrideRequest.ProtoReflect.Descriptor instead.
func (*DeletePetOverrideRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{31}
}

func (x *DeletePetOverrideRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// DeletePetOverrideResponse
type DeletePetOverrideResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePetOverrideResponse) Reset() {
	*x = DeletePetOverrideResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePetOverrideResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetOverrideResponse) ProtoMessage() {}

func (x *DeletePetOverrideResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetOverrideResponse.ProtoReflect.Descriptor instead.
func (*DeletePetOverrideResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{32}
}

// PetOverrideDef
type PetOverrideDef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// price
	Price *money.Money `protobuf:"bytes,3,opt,name=price,proto3,oneof" json:"price,omitempty"`
	// duration
	Duration      *int32 `protobuf:"varint,4,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PetOverrideDef) Reset() {
	*x = PetOverrideDef{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetOverrideDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetOverrideDef) ProtoMessage() {}

func (x *PetOverrideDef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetOverrideDef.ProtoReflect.Descriptor instead.
func (*PetOverrideDef) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{33}
}

func (x *PetOverrideDef) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetOverrideDef) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *PetOverrideDef) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *PetOverrideDef) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: no need page --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: no need page --)
//
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: no need page --)
//
// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: 无 parent 语义 --)
//
// list service category request
type ListServiceCategoriesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The organization type
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// The organization ID.
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// filter
	Filter        *ListServiceCategoriesRequest_Filter `protobuf:"bytes,18,opt,name=filter,proto3" json:"filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListServiceCategoriesRequest) Reset() {
	*x = ListServiceCategoriesRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListServiceCategoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceCategoriesRequest) ProtoMessage() {}

func (x *ListServiceCategoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceCategoriesRequest.ProtoReflect.Descriptor instead.
func (*ListServiceCategoriesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{34}
}

func (x *ListServiceCategoriesRequest) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *ListServiceCategoriesRequest) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *ListServiceCategoriesRequest) GetFilter() *ListServiceCategoriesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: no need page --)
//
// (-- api-linter: core::0132::response-unknown-fields=disabled
//
//	aip.dev/not-precedent: no need page --)
//
// list service setting services response
type ListServiceCategoriesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// category list
	Categories    []*ServiceCategory `protobuf:"bytes,1,rep,name=categories,proto3" json:"categories,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListServiceCategoriesResponse) Reset() {
	*x = ListServiceCategoriesResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListServiceCategoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceCategoriesResponse) ProtoMessage() {}

func (x *ListServiceCategoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceCategoriesResponse.ProtoReflect.Descriptor instead.
func (*ListServiceCategoriesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{35}
}

func (x *ListServiceCategoriesResponse) GetCategories() []*ServiceCategory {
	if x != nil {
		return x.Categories
	}
	return nil
}

// save categories request
type SaveServiceCategoriesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The type of the organization (e.g., company, enterprise).
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// The ID of the organization this category belongs to.
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// create category list
	CreateCategories []*SaveServiceCategoriesRequest_CategoryCreateDef `protobuf:"bytes,3,rep,name=create_categories,json=createCategories,proto3" json:"create_categories,omitempty"`
	// update category list
	UpdateCategories []*SaveServiceCategoriesRequest_CategoryUpdateDef `protobuf:"bytes,4,rep,name=update_categories,json=updateCategories,proto3" json:"update_categories,omitempty"`
	// delete ids category
	DeleteIds     []int64 `protobuf:"varint,5,rep,packed,name=delete_ids,json=deleteIds,proto3" json:"delete_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaveServiceCategoriesRequest) Reset() {
	*x = SaveServiceCategoriesRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveServiceCategoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveServiceCategoriesRequest) ProtoMessage() {}

func (x *SaveServiceCategoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveServiceCategoriesRequest.ProtoReflect.Descriptor instead.
func (*SaveServiceCategoriesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{36}
}

func (x *SaveServiceCategoriesRequest) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *SaveServiceCategoriesRequest) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *SaveServiceCategoriesRequest) GetCreateCategories() []*SaveServiceCategoriesRequest_CategoryCreateDef {
	if x != nil {
		return x.CreateCategories
	}
	return nil
}

func (x *SaveServiceCategoriesRequest) GetUpdateCategories() []*SaveServiceCategoriesRequest_CategoryUpdateDef {
	if x != nil {
		return x.UpdateCategories
	}
	return nil
}

func (x *SaveServiceCategoriesRequest) GetDeleteIds() []int64 {
	if x != nil {
		return x.DeleteIds
	}
	return nil
}

// save categories response
type SaveServiceCategoriesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaveServiceCategoriesResponse) Reset() {
	*x = SaveServiceCategoriesResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveServiceCategoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveServiceCategoriesResponse) ProtoMessage() {}

func (x *SaveServiceCategoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveServiceCategoriesResponse.ProtoReflect.Descriptor instead.
func (*SaveServiceCategoriesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{37}
}

// (-- api-linter: core::0231::request-unknown-fields=disabled
//
//	aip.dev/not-precedent: 根据 ID 及 condition 批量获取服务 --)
//
// (-- api-linter: core::0231::request-names-field=disabled
//
//	aip.dev/not-precedent: 根据 ID 及 condition 批量获取服务 --)
//
// batch get customized service request
type BatchGetCustomizedServicesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 当前的租户类型
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// 当前的租户 ID
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// query condition list
	QueryConditions []*BatchGetCustomizedServicesRequest_CustomizedServiceQueryCondition `protobuf:"bytes,3,rep,name=query_conditions,json=queryConditions,proto3" json:"query_conditions,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *BatchGetCustomizedServicesRequest) Reset() {
	*x = BatchGetCustomizedServicesRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetCustomizedServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetCustomizedServicesRequest) ProtoMessage() {}

func (x *BatchGetCustomizedServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetCustomizedServicesRequest.ProtoReflect.Descriptor instead.
func (*BatchGetCustomizedServicesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{38}
}

func (x *BatchGetCustomizedServicesRequest) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *BatchGetCustomizedServicesRequest) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *BatchGetCustomizedServicesRequest) GetQueryConditions() []*BatchGetCustomizedServicesRequest_CustomizedServiceQueryCondition {
	if x != nil {
		return x.QueryConditions
	}
	return nil
}

// (-- api-linter: core::0231::response-resource-field=disabled --)
// batch get customized service response
type BatchGetCustomizedServicesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// customized service list
	CustomizedServices []*BatchGetCustomizedServicesResponse_ServiceWithCustomized `protobuf:"bytes,1,rep,name=customized_services,json=customizedServices,proto3" json:"customized_services,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *BatchGetCustomizedServicesResponse) Reset() {
	*x = BatchGetCustomizedServicesResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetCustomizedServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetCustomizedServicesResponse) ProtoMessage() {}

func (x *BatchGetCustomizedServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetCustomizedServicesResponse.ProtoReflect.Descriptor instead.
func (*BatchGetCustomizedServicesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{39}
}

func (x *BatchGetCustomizedServicesResponse) GetCustomizedServices() []*BatchGetCustomizedServicesResponse_ServiceWithCustomized {
	if x != nil {
		return x.CustomizedServices
	}
	return nil
}

// list filter
type ListOBServicesRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// service ids
	ServiceIds    []int64 `protobuf:"varint,1,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListOBServicesRequest_Filter) Reset() {
	*x = ListOBServicesRequest_Filter{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOBServicesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOBServicesRequest_Filter) ProtoMessage() {}

func (x *ListOBServicesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOBServicesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListOBServicesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{9, 0}
}

func (x *ListOBServicesRequest_Filter) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

// list filter
type ListServicesRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// category ids
	CategoryIds []int64 `protobuf:"varint,1,rep,packed,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	// care type ids
	CareTypeIds []int64 `protobuf:"varint,2,rep,packed,name=care_type_ids,json=careTypeIds,proto3" json:"care_type_ids,omitempty"`
	// statuses
	Statuses      []Service_Status `protobuf:"varint,3,rep,packed,name=statuses,proto3,enum=backend.proto.offering.v1.Service_Status" json:"statuses,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListServicesRequest_Filter) Reset() {
	*x = ListServicesRequest_Filter{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListServicesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServicesRequest_Filter) ProtoMessage() {}

func (x *ListServicesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServicesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListServicesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{11, 0}
}

func (x *ListServicesRequest_Filter) GetCategoryIds() []int64 {
	if x != nil {
		return x.CategoryIds
	}
	return nil
}

func (x *ListServicesRequest_Filter) GetCareTypeIds() []int64 {
	if x != nil {
		return x.CareTypeIds
	}
	return nil
}

func (x *ListServicesRequest_Filter) GetStatuses() []Service_Status {
	if x != nil {
		return x.Statuses
	}
	return nil
}

// 可用性上下文控制选项。定义了动态查询的上下文，包括 Business/Staff/Lodging/Pet 等资源用于进行复杂的可用性过滤。
type ListAvailableServicesRequest_AvailabilityContext struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 当前选择的 Staff ID, 用于 Available staff 过滤。包含 Staff Override 信息
	StaffId *int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// 当前选择的 Lodging Unit ID，用于 Eligible lodging type 过滤
	LodgingUnitId *int64 `protobuf:"varint,3,opt,name=lodging_unit_id,json=lodgingUnitId,proto3,oneof" json:"lodging_unit_id,omitempty"`
	// 当前选择的 Pet ID，用于 Pet details 过滤，包括 Type&Breed/Weight/Pet Size/Pet Code/Coat Type，也包括 Pet Override 信息
	PetIds []int64 `protobuf:"varint,4,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
	// 当前选择的 Service ID, 查询的是该主服务下的“附加服务(Additional Service)”列表。
	ServiceIds    []int64 `protobuf:"varint,5,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAvailableServicesRequest_AvailabilityContext) Reset() {
	*x = ListAvailableServicesRequest_AvailabilityContext{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAvailableServicesRequest_AvailabilityContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAvailableServicesRequest_AvailabilityContext) ProtoMessage() {}

func (x *ListAvailableServicesRequest_AvailabilityContext) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAvailableServicesRequest_AvailabilityContext.ProtoReflect.Descriptor instead.
func (*ListAvailableServicesRequest_AvailabilityContext) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{16, 0}
}

func (x *ListAvailableServicesRequest_AvailabilityContext) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *ListAvailableServicesRequest_AvailabilityContext) GetLodgingUnitId() int64 {
	if x != nil && x.LodgingUnitId != nil {
		return *x.LodgingUnitId
	}
	return 0
}

func (x *ListAvailableServicesRequest_AvailabilityContext) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

func (x *ListAvailableServicesRequest_AvailabilityContext) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

// 过滤条件
type ListAvailableServicesRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 护理类型
	CareTypeIds []int64 `protobuf:"varint,1,rep,packed,name=care_type_ids,json=careTypeIds,proto3" json:"care_type_ids,omitempty"`
	// 分类
	CategoryIds []int64 `protobuf:"varint,2,rep,packed,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	// 状态（如：active/inactive 等）
	Statuses []Service_Status `protobuf:"varint,3,rep,packed,name=statuses,proto3,enum=backend.proto.offering.v1.Service_Status" json:"statuses,omitempty"`
	// 服务来源
	Sources []OfferingSource `protobuf:"varint,5,rep,packed,name=sources,proto3,enum=backend.proto.offering.v1.OfferingSource" json:"sources,omitempty"`
	// 关键词搜索（如服务名称等）
	Keyword       *string `protobuf:"bytes,4,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAvailableServicesRequest_Filter) Reset() {
	*x = ListAvailableServicesRequest_Filter{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAvailableServicesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAvailableServicesRequest_Filter) ProtoMessage() {}

func (x *ListAvailableServicesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAvailableServicesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListAvailableServicesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{16, 1}
}

func (x *ListAvailableServicesRequest_Filter) GetCareTypeIds() []int64 {
	if x != nil {
		return x.CareTypeIds
	}
	return nil
}

func (x *ListAvailableServicesRequest_Filter) GetCategoryIds() []int64 {
	if x != nil {
		return x.CategoryIds
	}
	return nil
}

func (x *ListAvailableServicesRequest_Filter) GetStatuses() []Service_Status {
	if x != nil {
		return x.Statuses
	}
	return nil
}

func (x *ListAvailableServicesRequest_Filter) GetSources() []OfferingSource {
	if x != nil {
		return x.Sources
	}
	return nil
}

func (x *ListAvailableServicesRequest_Filter) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

// list filter
type ListServiceCategoriesRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// category ids
	CategoryIds []int64 `protobuf:"varint,2,rep,packed,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	// care type ids
	CareTypeIds   []int64 `protobuf:"varint,3,rep,packed,name=care_type_ids,json=careTypeIds,proto3" json:"care_type_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListServiceCategoriesRequest_Filter) Reset() {
	*x = ListServiceCategoriesRequest_Filter{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListServiceCategoriesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceCategoriesRequest_Filter) ProtoMessage() {}

func (x *ListServiceCategoriesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceCategoriesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListServiceCategoriesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{34, 0}
}

func (x *ListServiceCategoriesRequest_Filter) GetCategoryIds() []int64 {
	if x != nil {
		return x.CategoryIds
	}
	return nil
}

func (x *ListServiceCategoriesRequest_Filter) GetCareTypeIds() []int64 {
	if x != nil {
		return x.CareTypeIds
	}
	return nil
}

// Defines the structure for a service category, used to organize services.
type SaveServiceCategoriesRequest_CategoryUpdateDef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Primary key ID of the service category.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The ID of the care type associated with this category.
	CareTypeId int64 `protobuf:"varint,2,opt,name=care_type_id,json=careTypeId,proto3" json:"care_type_id,omitempty"`
	// The name of the service category, unique within the same organization and care type.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// sort order of the category
	Sort          int64 `protobuf:"varint,4,opt,name=sort,proto3" json:"sort,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaveServiceCategoriesRequest_CategoryUpdateDef) Reset() {
	*x = SaveServiceCategoriesRequest_CategoryUpdateDef{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveServiceCategoriesRequest_CategoryUpdateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveServiceCategoriesRequest_CategoryUpdateDef) ProtoMessage() {}

func (x *SaveServiceCategoriesRequest_CategoryUpdateDef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveServiceCategoriesRequest_CategoryUpdateDef.ProtoReflect.Descriptor instead.
func (*SaveServiceCategoriesRequest_CategoryUpdateDef) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{36, 0}
}

func (x *SaveServiceCategoriesRequest_CategoryUpdateDef) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SaveServiceCategoriesRequest_CategoryUpdateDef) GetCareTypeId() int64 {
	if x != nil {
		return x.CareTypeId
	}
	return 0
}

func (x *SaveServiceCategoriesRequest_CategoryUpdateDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SaveServiceCategoriesRequest_CategoryUpdateDef) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

// Defines the structure for a service category, used to organize services.
type SaveServiceCategoriesRequest_CategoryCreateDef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The ID of the care type associated with this category.
	CareTypeId int64 `protobuf:"varint,2,opt,name=care_type_id,json=careTypeId,proto3" json:"care_type_id,omitempty"`
	// The name of the service category, unique within the same organization and care type.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// sort order of the category
	Sort          int64 `protobuf:"varint,4,opt,name=sort,proto3" json:"sort,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaveServiceCategoriesRequest_CategoryCreateDef) Reset() {
	*x = SaveServiceCategoriesRequest_CategoryCreateDef{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveServiceCategoriesRequest_CategoryCreateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveServiceCategoriesRequest_CategoryCreateDef) ProtoMessage() {}

func (x *SaveServiceCategoriesRequest_CategoryCreateDef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveServiceCategoriesRequest_CategoryCreateDef.ProtoReflect.Descriptor instead.
func (*SaveServiceCategoriesRequest_CategoryCreateDef) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{36, 1}
}

func (x *SaveServiceCategoriesRequest_CategoryCreateDef) GetCareTypeId() int64 {
	if x != nil {
		return x.CareTypeId
	}
	return 0
}

func (x *SaveServiceCategoriesRequest_CategoryCreateDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SaveServiceCategoriesRequest_CategoryCreateDef) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

// customized service query condition
type BatchGetCustomizedServicesRequest_CustomizedServiceQueryCondition struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 虚拟 id，标识一个 condition
	VirtualId int64 `protobuf:"varint,1,opt,name=virtual_id,json=virtualId,proto3" json:"virtual_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// pet id
	PetId *int64 `protobuf:"varint,4,opt,name=pet_id,json=petId,proto3,oneof" json:"pet_id,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,5,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// customer address zipcode
	Zipcode *string `protobuf:"bytes,6,opt,name=zipcode,proto3,oneof" json:"zipcode,omitempty"`
	// customer address coordinate
	Coordinate    *latlng.LatLng `protobuf:"bytes,7,opt,name=coordinate,proto3,oneof" json:"coordinate,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchGetCustomizedServicesRequest_CustomizedServiceQueryCondition) Reset() {
	*x = BatchGetCustomizedServicesRequest_CustomizedServiceQueryCondition{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetCustomizedServicesRequest_CustomizedServiceQueryCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetCustomizedServicesRequest_CustomizedServiceQueryCondition) ProtoMessage() {}

func (x *BatchGetCustomizedServicesRequest_CustomizedServiceQueryCondition) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetCustomizedServicesRequest_CustomizedServiceQueryCondition.ProtoReflect.Descriptor instead.
func (*BatchGetCustomizedServicesRequest_CustomizedServiceQueryCondition) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{38, 0}
}

func (x *BatchGetCustomizedServicesRequest_CustomizedServiceQueryCondition) GetVirtualId() int64 {
	if x != nil {
		return x.VirtualId
	}
	return 0
}

func (x *BatchGetCustomizedServicesRequest_CustomizedServiceQueryCondition) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *BatchGetCustomizedServicesRequest_CustomizedServiceQueryCondition) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *BatchGetCustomizedServicesRequest_CustomizedServiceQueryCondition) GetPetId() int64 {
	if x != nil && x.PetId != nil {
		return *x.PetId
	}
	return 0
}

func (x *BatchGetCustomizedServicesRequest_CustomizedServiceQueryCondition) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *BatchGetCustomizedServicesRequest_CustomizedServiceQueryCondition) GetZipcode() string {
	if x != nil && x.Zipcode != nil {
		return *x.Zipcode
	}
	return ""
}

func (x *BatchGetCustomizedServicesRequest_CustomizedServiceQueryCondition) GetCoordinate() *latlng.LatLng {
	if x != nil {
		return x.Coordinate
	}
	return nil
}

// customized service
type BatchGetCustomizedServicesResponse_ServiceWithCustomized struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// virtual id
	VirtualId int64 `protobuf:"varint,1,opt,name=virtual_id,json=virtualId,proto3" json:"virtual_id,omitempty"`
	// customized service
	CustomizedService *CustomizedService `protobuf:"bytes,2,opt,name=customized_service,json=customizedService,proto3" json:"customized_service,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *BatchGetCustomizedServicesResponse_ServiceWithCustomized) Reset() {
	*x = BatchGetCustomizedServicesResponse_ServiceWithCustomized{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetCustomizedServicesResponse_ServiceWithCustomized) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetCustomizedServicesResponse_ServiceWithCustomized) ProtoMessage() {}

func (x *BatchGetCustomizedServicesResponse_ServiceWithCustomized) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetCustomizedServicesResponse_ServiceWithCustomized.ProtoReflect.Descriptor instead.
func (*BatchGetCustomizedServicesResponse_ServiceWithCustomized) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{39, 0}
}

func (x *BatchGetCustomizedServicesResponse_ServiceWithCustomized) GetVirtualId() int64 {
	if x != nil {
		return x.VirtualId
	}
	return 0
}

func (x *BatchGetCustomizedServicesResponse_ServiceWithCustomized) GetCustomizedService() *CustomizedService {
	if x != nil {
		return x.CustomizedService
	}
	return nil
}

var File_backend_proto_offering_v1_service_service_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_service_service_proto_rawDesc = "" +
	"\n" +
	"/backend/proto/offering/v1/service_service.proto\x12\x19backend.proto.offering.v1\x1a\x18google/type/latlng.proto\x1a&backend/proto/offering/v1/common.proto\x1a'backend/proto/offering/v1/service.proto\x1a2backend/proto/offering/v1/service_ob_setting.proto\x1a0backend/proto/organization/v1/organization.proto\x1a\x1bbuf/validate/validate.proto\x1a\x17google/type/money.proto\x1a0backend/proto/offering/v1/service_category.proto\">\n" +
	"\x14DeleteServiceRequest\x12&\n" +
	"\n" +
	"service_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tserviceId\"\x17\n" +
	"\x15DeleteServiceResponse\"e\n" +
	"\x14UpdateServiceRequest\x12M\n" +
	"\aservice\x18\x01 \x01(\v2+.backend.proto.offering.v1.ServiceUpdateDefB\x06\xbaH\x03\xc8\x01\x01R\aservice\"\x17\n" +
	"\x15UpdateServiceResponse\"e\n" +
	"\x14CreateServiceRequest\x12M\n" +
	"\aservice\x18\x01 \x01(\v2+.backend.proto.offering.v1.ServiceCreateDefB\x06\xbaH\x03\xc8\x01\x01R\aservice\"6\n" +
	"\x15CreateServiceResponse\x12\x1d\n" +
	"\n" +
	"service_id\x18\x01 \x01(\x03R\tserviceId\";\n" +
	"\x11GetServiceRequest\x12&\n" +
	"\n" +
	"service_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tserviceId\"R\n" +
	"\x12GetServiceResponse\x12<\n" +
	"\aservice\x18\x01 \x01(\v2\".backend.proto.offering.v1.ServiceR\aservice\"\x86\x01\n" +
	"\x0fCategoryService\x12\x1f\n" +
	"\vcategory_id\x18\x01 \x01(\x03R\n" +
	"categoryId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12>\n" +
	"\bservices\x18\x03 \x03(\v2\".backend.proto.offering.v1.ServiceR\bservices\"\x97\x02\n" +
	"\x15ListOBServicesRequest\x12+\n" +
	"\vbusiness_id\x18\x01 \x01(\x03B\n" +
	"\xbaH\a\xc8\x01\x01\"\x02 \x00R\n" +
	"businessId\x12O\n" +
	"\x06filter\x18\x02 \x01(\v27.backend.proto.offering.v1.ListOBServicesRequest.FilterR\x06filter\x12H\n" +
	"\n" +
	"pagination\x18\t \x01(\v2(.backend.proto.offering.v1.PaginationRefR\n" +
	"pagination\x1a6\n" +
	"\x06Filter\x12,\n" +
	"\vservice_ids\x18\x01 \x03(\x03B\v\xbaH\b\x92\x01\x05\b\x00\x10\xe8\aR\n" +
	"serviceIds\"\xe9\x01\n" +
	"\x16ListOBServicesResponse\x12[\n" +
	"\x13service_ob_settings\x18\x01 \x03(\v2+.backend.proto.offering.v1.ServiceOBSettingR\x11serviceObSettings\x12M\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2(.backend.proto.offering.v1.PaginationRefH\x00R\n" +
	"pagination\x88\x01\x01\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05totalB\r\n" +
	"\v_pagination\"\x8d\x04\n" +
	"\x13ListServicesRequest\x12k\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeB\r\xbaH\n" +
	"\xc8\x01\x01\x82\x01\x04\x10\x01 \x00R\x10organizationType\x123\n" +
	"\x0forganization_id\x18\x02 \x01(\x03B\n" +
	"\xbaH\a\xc8\x01\x01\"\x02 \x00R\x0eorganizationId\x12M\n" +
	"\x06filter\x18\x03 \x01(\v25.backend.proto.offering.v1.ListServicesRequest.FilterR\x06filter\x12H\n" +
	"\n" +
	"pagination\x18\x05 \x01(\v2(.backend.proto.offering.v1.PaginationRefR\n" +
	"pagination\x1a\xba\x01\n" +
	"\x06Filter\x12-\n" +
	"\fcategory_ids\x18\x01 \x03(\x03B\n" +
	"\xbaH\a\x92\x01\x04\b\x00\x10dR\vcategoryIds\x12.\n" +
	"\rcare_type_ids\x18\x02 \x03(\x03B\n" +
	"\xbaH\a\x92\x01\x04\b\x00\x10dR\vcareTypeIds\x12Q\n" +
	"\bstatuses\x18\x03 \x03(\x0e2).backend.proto.offering.v1.Service.StatusB\n" +
	"\xbaH\a\x92\x01\x04\b\x00\x10\n" +
	"R\bstatuses\"\xca\x01\n" +
	"\x14ListServicesResponse\x12>\n" +
	"\bservices\x18\x01 \x03(\v2\".backend.proto.offering.v1.ServiceR\bservices\x12M\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2(.backend.proto.offering.v1.PaginationRefH\x00R\n" +
	"pagination\x88\x01\x01\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05totalB\r\n" +
	"\v_pagination\"\x9b\x02\n" +
	"\x1aBatchUpdateServicesRequest\x12h\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00R\x10organizationType\x120\n" +
	"\x0forganization_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x0eorganizationId\x12a\n" +
	"\x0fupdate_services\x18\x03 \x03(\v2+.backend.proto.offering.v1.ServiceUpdateDefB\v\xbaH\b\x92\x01\x05\b\x01\x10\xe8\aR\x0eupdateServices\"\xd4\f\n" +
	"\x10ServiceUpdateDef\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\x12-\n" +
	"\vcategory_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x00R\n" +
	"categoryId\x88\x01\x01\x12 \n" +
	"\x04name\x18\x03 \x01(\tB\a\xbaH\x04r\x02\x18dH\x01R\x04name\x88\x01\x01\x12/\n" +
	"\vdescription\x18\x04 \x01(\tB\b\xbaH\x05r\x03\x18\xe8\aH\x02R\vdescription\x88\x01\x01\x12+\n" +
	"\n" +
	"color_code\x18\x05 \x01(\tB\a\xbaH\x04r\x02\x18\n" +
	"H\x03R\tcolorCode\x88\x01\x01\x12 \n" +
	"\x04sort\x18\x06 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x04R\x04sort\x88\x01\x01\x12,\n" +
	"\x06images\x18\a \x03(\tB\x14\xbaH\x11\x92\x01\x0e\b\x00\x10\xe8\a\x18\x01\"\x05r\x03\x18\x80\bR\x06images\x12R\n" +
	"\x06status\x18\b \x01(\x0e2).backend.proto.offering.v1.Service.StatusB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00H\x05R\x06status\x88\x01\x01\x12`\n" +
	"\x12available_business\x18\t \x01(\v2,.backend.proto.offering.v1.AvailableBusinessH\x06R\x11availableBusiness\x88\x01\x01\x12Q\n" +
	"\n" +
	"attributes\x18\n" +
	" \x01(\v2,.backend.proto.offering.v1.ServiceAttributesH\aR\n" +
	"attributes\x88\x01\x01\x12`\n" +
	"\x12additional_service\x18\v \x01(\v2,.backend.proto.offering.v1.AdditionalServiceH\bR\x11additionalService\x88\x01\x01\x12g\n" +
	"\x14available_type_breed\x18\f \x01(\v20.backend.proto.offering.v1.AvailablePetTypeBreedH\tR\x12availableTypeBreed\x88\x01\x01\x12^\n" +
	"\x12available_pet_size\x18\r \x01(\v2+.backend.proto.offering.v1.AvailablePetSizeH\n" +
	"R\x10availablePetSize\x88\x01\x01\x12a\n" +
	"\x13available_coat_type\x18\x0e \x01(\v2,.backend.proto.offering.v1.AvailableCoatTypeH\vR\x11availableCoatType\x88\x01\x01\x12^\n" +
	"\x12available_pet_code\x18\x0f \x01(\v2+.backend.proto.offering.v1.AvailablePetCodeH\fR\x10availablePetCode\x88\x01\x01\x12d\n" +
	"\x14available_pet_weight\x18\x10 \x01(\v2-.backend.proto.offering.v1.AvailablePetWeightH\rR\x12availablePetWeight\x88\x01\x01\x12-\n" +
	"\x05price\x18\x11 \x01(\v2\x12.google.type.MoneyH\x0eR\x05price\x88\x01\x01\x12\x1a\n" +
	"\x06tax_id\x18\x13 \x01(\x03H\x0fR\x05taxId\x88\x01\x01\x12j\n" +
	"\x18business_staff_overrides\x18\x14 \x03(\v20.backend.proto.offering.v1.BusinessStaffOverrideR\x16businessStaffOverridesB\x0e\n" +
	"\f_category_idB\a\n" +
	"\x05_nameB\x0e\n" +
	"\f_descriptionB\r\n" +
	"\v_color_codeB\a\n" +
	"\x05_sortB\t\n" +
	"\a_statusB\x15\n" +
	"\x13_available_businessB\r\n" +
	"\v_attributesB\x15\n" +
	"\x13_additional_serviceB\x17\n" +
	"\x15_available_type_breedB\x15\n" +
	"\x13_available_pet_sizeB\x16\n" +
	"\x14_available_coat_typeB\x15\n" +
	"\x13_available_pet_codeB\x17\n" +
	"\x15_available_pet_weightB\b\n" +
	"\x06_priceB\t\n" +
	"\a_tax_id\"\x1d\n" +
	"\x1bBatchUpdateServicesResponse\"\xc9\b\n" +
	"\x1cListAvailableServicesRequest\x12h\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00R\x10organizationType\x120\n" +
	"\x0forganization_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x0eorganizationId\x12e\n" +
	"\acontext\x18\x03 \x01(\v2K.backend.proto.offering.v1.ListAvailableServicesRequest.AvailabilityContextR\acontext\x12[\n" +
	"\x06filter\x18\x05 \x01(\v2>.backend.proto.offering.v1.ListAvailableServicesRequest.FilterH\x00R\x06filter\x88\x01\x01\x12M\n" +
	"\n" +
	"pagination\x18\x06 \x01(\v2(.backend.proto.offering.v1.PaginationRefH\x01R\n" +
	"pagination\x88\x01\x01\x1a\xf7\x01\n" +
	"\x13AvailabilityContext\x12'\n" +
	"\bstaff_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x00R\astaffId\x88\x01\x01\x124\n" +
	"\x0flodging_unit_id\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x01R\rlodgingUnitId\x88\x01\x01\x12+\n" +
	"\apet_ids\x18\x04 \x03(\x03B\x12\xbaH\x0f\x92\x01\f\b\x00\x10d\x18\x01\"\x04\"\x02 \x00R\x06petIds\x123\n" +
	"\vservice_ids\x18\x05 \x03(\x03B\x12\xbaH\x0f\x92\x01\f\b\x00\x10d\x18\x01\"\x04\"\x02 \x00R\n" +
	"serviceIdsB\v\n" +
	"\t_staff_idB\x12\n" +
	"\x10_lodging_unit_id\x1a\xe5\x02\n" +
	"\x06Filter\x126\n" +
	"\rcare_type_ids\x18\x01 \x03(\x03B\x12\xbaH\x0f\x92\x01\f\b\x00\x10d\x18\x01\"\x04\"\x02 \x00R\vcareTypeIds\x125\n" +
	"\fcategory_ids\x18\x02 \x03(\x03B\x12\xbaH\x0f\x92\x01\f\b\x00\x10d\x18\x01\"\x04\"\x02 \x00R\vcategoryIds\x12\\\n" +
	"\bstatuses\x18\x03 \x03(\x0e2).backend.proto.offering.v1.Service.StatusB\x15\xbaH\x12\x92\x01\x0f\b\x00\x10\n" +
	"\x18\x01\"\a\x82\x01\x04\x10\x01 \x00R\bstatuses\x12Z\n" +
	"\asources\x18\x05 \x03(\x0e2).backend.proto.offering.v1.OfferingSourceB\x15\xbaH\x12\x92\x01\x0f\b\x00\x10d\x18\x01\"\a\x82\x01\x04\x10\x01 \x00R\asources\x12&\n" +
	"\akeyword\x18\x04 \x01(\tB\a\xbaH\x04r\x02\x18dH\x00R\akeyword\x88\x01\x01B\n" +
	"\n" +
	"\b_keywordB\t\n" +
	"\a_filterB\r\n" +
	"\v_pagination\"\xdc\x01\n" +
	"\x1dListAvailableServicesResponse\x12G\n" +
	"\bservices\x18\x01 \x03(\v2+.backend.proto.offering.v1.AvailableServiceR\bservices\x12M\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2(.backend.proto.offering.v1.PaginationRefH\x00R\n" +
	"pagination\x88\x01\x01\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05totalB\r\n" +
	"\v_pagination\"\xd1\x05\n" +
	"\x10AvailableService\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12 \n" +
	"\fcare_type_id\x18\x02 \x01(\x03R\n" +
	"careTypeId\x12\x1f\n" +
	"\vcategory_id\x18\x03 \x01(\x03R\n" +
	"categoryId\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x05 \x01(\tR\vdescription\x12\x1d\n" +
	"\n" +
	"color_code\x18\x06 \x01(\tR\tcolorCode\x12\x12\n" +
	"\x04sort\x18\a \x01(\x03R\x04sort\x12\x16\n" +
	"\x06images\x18\b \x03(\tR\x06images\x12A\n" +
	"\x06source\x18\t \x01(\x0e2).backend.proto.offering.v1.OfferingSourceR\x06source\x12A\n" +
	"\x06status\x18\n" +
	" \x01(\x0e2).backend.proto.offering.v1.Service.StatusR\x06status\x12(\n" +
	"\x05price\x18\v \x01(\v2\x12.google.type.MoneyR\x05price\x12\x15\n" +
	"\x06tax_id\x18\f \x01(\x03R\x05taxId\x12Z\n" +
	"\n" +
	"attributes\x18\r \x01(\v25.backend.proto.offering.v1.AvailableServiceAttributesH\x00R\n" +
	"attributes\x88\x01\x01\x12j\n" +
	"\x18business_staff_overrides\x18\x0e \x03(\v20.backend.proto.offering.v1.BusinessStaffOverrideR\x16businessStaffOverrides\x12K\n" +
	"\rpet_overrides\x18\x0f \x03(\v2&.backend.proto.offering.v1.PetOverrideR\fpetOverridesB\r\n" +
	"\v_attributes\"\xd0\x02\n" +
	"\x1aAvailableServiceAttributes\x12\x1f\n" +
	"\bduration\x18\x01 \x01(\x05H\x00R\bduration\x88\x01\x01\x12&\n" +
	"\fmax_duration\x18\x02 \x01(\x05H\x01R\vmaxDuration\x88\x01\x01\x12/\n" +
	"\x11is_required_staff\x18\x03 \x01(\bH\x02R\x0fisRequiredStaff\x88\x01\x01\x12\x1e\n" +
	"\bob_alias\x18\x04 \x01(\tH\x03R\aobAlias\x88\x01\x01\x12H\n" +
	"\n" +
	"price_unit\x18\x05 \x01(\x0e2$.backend.proto.offering.v1.PriceUnitH\x04R\tpriceUnit\x88\x01\x01B\v\n" +
	"\t_durationB\x0f\n" +
	"\r_max_durationB\x14\n" +
	"\x12_is_required_staffB\v\n" +
	"\t_ob_aliasB\r\n" +
	"\v_price_unit\"\xb2\x04\n" +
	"\x16UpdateOBServiceRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x02 \x01(\x03R\n" +
	"businessId\x12&\n" +
	"\n" +
	"service_id\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tserviceId\x12&\n" +
	"\fis_available\x18\v \x01(\bH\x00R\visAvailable\x88\x01\x01\x12j\n" +
	"\x0fshow_base_price\x18\f \x01(\x0e2=.backend.proto.offering.v1.ServiceOBSetting.ShowBasePriceModeH\x01R\rshowBasePrice\x88\x01\x01\x12%\n" +
	"\fis_all_staff\x18\r \x01(\bH\x02R\n" +
	"isAllStaff\x88\x01\x01\x12\x1b\n" +
	"\tstaff_ids\x18\x0e \x03(\x03R\bstaffIds\x12b\n" +
	"\rshow_duration\x18\x0f \x01(\x0e28.backend.proto.offering.v1.ServiceOBSetting.ShowDurationH\x03R\fshowDuration\x88\x01\x01\x123\n" +
	"\x13allow_group_booking\x18\x10 \x01(\bH\x04R\x11allowGroupBooking\x88\x01\x01B\x0f\n" +
	"\r_is_availableB\x12\n" +
	"\x10_show_base_priceB\x0f\n" +
	"\r_is_all_staffB\x10\n" +
	"\x0e_show_durationB\x16\n" +
	"\x14_allow_group_booking\"\x19\n" +
	"\x17UpdateOBServiceResponse\"\xdb\x01\n" +
	"\x17BatchGetServicesRequest\x12h\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00R\x10organizationType\x120\n" +
	"\x0forganization_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x0eorganizationId\x12$\n" +
	"\x03ids\x18\x03 \x03(\x03B\x12\xbaH\x0f\x92\x01\f\b\x01\x10d\x18\x01\"\x04\"\x02 \x00R\x03ids\"Z\n" +
	"\x18BatchGetServicesResponse\x12>\n" +
	"\bservices\x18\x01 \x03(\v2\".backend.proto.offering.v1.ServiceR\bservices\"\xeb\r\n" +
	"\x10ServiceCreateDef\x12k\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeB\r\xbaH\n" +
	"\xc8\x01\x01\x82\x01\x04\x10\x01 \x00R\x10organizationType\x123\n" +
	"\x0forganization_id\x18\x02 \x01(\x03B\n" +
	"\xbaH\a\xc8\x01\x01\"\x02 \x00R\x0eorganizationId\x12,\n" +
	"\fcare_type_id\x18\x03 \x01(\x03B\n" +
	"\xbaH\a\xc8\x01\x01\"\x02 \x00R\n" +
	"careTypeId\x12-\n" +
	"\vcategory_id\x18\x04 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x00R\n" +
	"categoryId\x88\x01\x01\x12 \n" +
	"\x04name\x18\x05 \x01(\tB\f\xbaH\t\xc8\x01\x01r\x04\x10\x01\x18dR\x04name\x12/\n" +
	"\vdescription\x18\x06 \x01(\tB\b\xbaH\x05r\x03\x18\xe8\aH\x01R\vdescription\x88\x01\x01\x12+\n" +
	"\n" +
	"color_code\x18\a \x01(\tB\f\xbaH\t\xc8\x01\x01r\x04\x10\x01\x18\n" +
	"R\tcolorCode\x12,\n" +
	"\x06images\x18\t \x03(\tB\x14\xbaH\x11\x92\x01\x0e\b\x00\x10\xe8\a\x18\x01\"\x05r\x03\x18\x80\bR\x06images\x12P\n" +
	"\x06source\x18\n" +
	" \x01(\x0e2).backend.proto.offering.v1.OfferingSourceB\r\xbaH\n" +
	"\xc8\x01\x01\x82\x01\x04\x10\x01 \x00R\x06source\x12P\n" +
	"\x06status\x18\v \x01(\x0e2).backend.proto.offering.v1.Service.StatusB\r\xbaH\n" +
	"\xc8\x01\x01\x82\x01\x04\x10\x01 \x00R\x06status\x12c\n" +
	"\x12available_business\x18\r \x01(\v2,.backend.proto.offering.v1.AvailableBusinessB\x06\xbaH\x03\xc8\x01\x01R\x11availableBusiness\x12`\n" +
	"\x12additional_service\x18\x0e \x01(\v2,.backend.proto.offering.v1.AdditionalServiceH\x02R\x11additionalService\x88\x01\x01\x12g\n" +
	"\x14available_type_breed\x18\x0f \x01(\v20.backend.proto.offering.v1.AvailablePetTypeBreedH\x03R\x12availableTypeBreed\x88\x01\x01\x12^\n" +
	"\x12available_pet_size\x18\x10 \x01(\v2+.backend.proto.offering.v1.AvailablePetSizeH\x04R\x10availablePetSize\x88\x01\x01\x12a\n" +
	"\x13available_coat_type\x18\x11 \x01(\v2,.backend.proto.offering.v1.AvailableCoatTypeH\x05R\x11availableCoatType\x88\x01\x01\x12^\n" +
	"\x12available_pet_code\x18\x12 \x01(\v2+.backend.proto.offering.v1.AvailablePetCodeH\x06R\x10availablePetCode\x88\x01\x01\x12Q\n" +
	"\n" +
	"attributes\x18\x13 \x01(\v2,.backend.proto.offering.v1.ServiceAttributesH\aR\n" +
	"attributes\x88\x01\x01\x12d\n" +
	"\x14available_pet_weight\x18\x14 \x01(\v2-.backend.proto.offering.v1.AvailablePetWeightH\bR\x12availablePetWeight\x88\x01\x01\x120\n" +
	"\x05price\x18\x15 \x01(\v2\x12.google.type.MoneyB\x06\xbaH\x03\xc8\x01\x01R\x05price\x12\x1e\n" +
	"\x06tax_id\x18\x17 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x05taxId\x12j\n" +
	"\x18business_staff_overrides\x18\x19 \x03(\v20.backend.proto.offering.v1.BusinessStaffOverrideR\x16businessStaffOverridesB\x0e\n" +
	"\f_category_idB\x0e\n" +
	"\f_descriptionB\x15\n" +
	"\x13_additional_serviceB\x17\n" +
	"\x15_available_type_breedB\x15\n" +
	"\x13_available_pet_sizeB\x16\n" +
	"\x14_available_coat_typeB\x15\n" +
	"\x13_available_pet_codeB\r\n" +
	"\v_attributesB\x17\n" +
	"\x15_available_pet_weight\"\x8f\x01\n" +
	"\x16ListPetOverrideRequest\x12+\n" +
	"\apet_ids\x18\x01 \x03(\x03B\x12\xbaH\x0f\x92\x01\f\b\x01\x10d\x18\x01\"\x04\"\x02 \x00R\x06petIds\x12H\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2(.backend.proto.offering.v1.PaginationRefR\n" +
	"pagination\"\xc6\x01\n" +
	"\x17ListPetOverrideResponse\x12K\n" +
	"\rpet_overrides\x18\x01 \x03(\v2&.backend.proto.offering.v1.PetOverrideR\fpetOverrides\x12H\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2(.backend.proto.offering.v1.PaginationRefR\n" +
	"pagination\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05total\"p\n" +
	"\x18UpsertPetOverrideRequest\x12T\n" +
	"\fpet_override\x18\x01 \x01(\v2).backend.proto.offering.v1.PetOverrideDefB\x06\xbaH\x03\xc8\x01\x01R\vpetOverride\"f\n" +
	"\x19UpsertPetOverrideResponse\x12I\n" +
	"\fpet_override\x18\x01 \x01(\v2&.backend.proto.offering.v1.PetOverrideR\vpetOverride\"\x89\x01\n" +
	"\x18UpdatePetOverrideRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\x12T\n" +
	"\fpet_override\x18\x02 \x01(\v2).backend.proto.offering.v1.PetOverrideDefB\x06\xbaH\x03\xc8\x01\x01R\vpetOverride\"f\n" +
	"\x19UpdatePetOverrideResponse\x12I\n" +
	"\fpet_override\x18\x01 \x01(\v2&.backend.proto.offering.v1.PetOverrideR\vpetOverride\"3\n" +
	"\x18DeletePetOverrideRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"\x1b\n" +
	"\x19DeletePetOverrideResponse\"\xbf\x01\n" +
	"\x0ePetOverrideDef\x12\x1e\n" +
	"\x06pet_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x05petId\x12&\n" +
	"\n" +
	"service_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tserviceId\x12-\n" +
	"\x05price\x18\x03 \x01(\v2\x12.google.type.MoneyH\x00R\x05price\x88\x01\x01\x12\x1f\n" +
	"\bduration\x18\x04 \x01(\x05H\x01R\bduration\x88\x01\x01B\b\n" +
	"\x06_priceB\v\n" +
	"\t_duration\"\xce\x02\n" +
	"\x1cListServiceCategoriesRequest\x12\\\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeR\x10organizationType\x12'\n" +
	"\x0forganization_id\x18\x02 \x01(\x03R\x0eorganizationId\x12V\n" +
	"\x06filter\x18\x12 \x01(\v2>.backend.proto.offering.v1.ListServiceCategoriesRequest.FilterR\x06filter\x1aO\n" +
	"\x06Filter\x12!\n" +
	"\fcategory_ids\x18\x02 \x03(\x03R\vcategoryIds\x12\"\n" +
	"\rcare_type_ids\x18\x03 \x03(\x03R\vcareTypeIds\"k\n" +
	"\x1dListServiceCategoriesResponse\x12J\n" +
	"\n" +
	"categories\x18\x01 \x03(\v2*.backend.proto.offering.v1.ServiceCategoryR\n" +
	"categories\"\x82\x05\n" +
	"\x1cSaveServiceCategoriesRequest\x12\\\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeR\x10organizationType\x12'\n" +
	"\x0forganization_id\x18\x02 \x01(\x03R\x0eorganizationId\x12v\n" +
	"\x11create_categories\x18\x03 \x03(\v2I.backend.proto.offering.v1.SaveServiceCategoriesRequest.CategoryCreateDefR\x10createCategories\x12v\n" +
	"\x11update_categories\x18\x04 \x03(\v2I.backend.proto.offering.v1.SaveServiceCategoriesRequest.CategoryUpdateDefR\x10updateCategories\x12\x1d\n" +
	"\n" +
	"delete_ids\x18\x05 \x03(\x03R\tdeleteIds\x1am\n" +
	"\x11CategoryUpdateDef\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12 \n" +
	"\fcare_type_id\x18\x02 \x01(\x03R\n" +
	"careTypeId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x12\n" +
	"\x04sort\x18\x04 \x01(\x03R\x04sort\x1a]\n" +
	"\x11CategoryCreateDef\x12 \n" +
	"\fcare_type_id\x18\x02 \x01(\x03R\n" +
	"careTypeId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x12\n" +
	"\x04sort\x18\x04 \x01(\x03R\x04sort\"\x1f\n" +
	"\x1dSaveServiceCategoriesResponse\"\xed\x05\n" +
	"!BatchGetCustomizedServicesRequest\x12h\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00R\x10organizationType\x120\n" +
	"\x0forganization_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x0eorganizationId\x12\x95\x01\n" +
	"\x10query_conditions\x18\x03 \x03(\v2\\.backend.proto.offering.v1.BatchGetCustomizedServicesRequest.CustomizedServiceQueryConditionB\f\xbaH\t\x92\x01\x06\b\x01\x10d\x18\x01R\x0fqueryConditions\x1a\x93\x03\n" +
	"\x1fCustomizedServiceQueryCondition\x12&\n" +
	"\n" +
	"virtual_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tvirtualId\x12&\n" +
	"\n" +
	"service_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tserviceId\x12-\n" +
	"\vbusiness_id\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x00R\n" +
	"businessId\x88\x01\x01\x12#\n" +
	"\x06pet_id\x18\x04 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x01R\x05petId\x88\x01\x01\x12'\n" +
	"\bstaff_id\x18\x05 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x02R\astaffId\x88\x01\x01\x12&\n" +
	"\azipcode\x18\x06 \x01(\tB\a\xbaH\x04r\x02\x182H\x03R\azipcode\x88\x01\x01\x128\n" +
	"\n" +
	"coordinate\x18\a \x01(\v2\x13.google.type.LatLngH\x04R\n" +
	"coordinate\x88\x01\x01B\x0e\n" +
	"\f_business_idB\t\n" +
	"\a_pet_idB\v\n" +
	"\t_staff_idB\n" +
	"\n" +
	"\b_zipcodeB\r\n" +
	"\v_coordinate\"\xca\x02\n" +
	"\"BatchGetCustomizedServicesResponse\x12\x84\x01\n" +
	"\x13customized_services\x18\x01 \x03(\v2S.backend.proto.offering.v1.BatchGetCustomizedServicesResponse.ServiceWithCustomizedR\x12customizedServices\x1a\x9c\x01\n" +
	"\x15ServiceWithCustomized\x12&\n" +
	"\n" +
	"virtual_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tvirtualId\x12[\n" +
	"\x12customized_service\x18\x02 \x01(\v2,.backend.proto.offering.v1.CustomizedServiceR\x11customizedService2\xfa\x10\n" +
	"\x0eServiceService\x12r\n" +
	"\rCreateService\x12/.backend.proto.offering.v1.CreateServiceRequest\x1a0.backend.proto.offering.v1.CreateServiceResponse\x12i\n" +
	"\n" +
	"GetService\x12,.backend.proto.offering.v1.GetServiceRequest\x1a-.backend.proto.offering.v1.GetServiceResponse\x12r\n" +
	"\rUpdateService\x12/.backend.proto.offering.v1.UpdateServiceRequest\x1a0.backend.proto.offering.v1.UpdateServiceResponse\x12r\n" +
	"\rDeleteService\x12/.backend.proto.offering.v1.DeleteServiceRequest\x1a0.backend.proto.offering.v1.DeleteServiceResponse\x12o\n" +
	"\fListServices\x12..backend.proto.offering.v1.ListServicesRequest\x1a/.backend.proto.offering.v1.ListServicesResponse\x12u\n" +
	"\x0eListOBServices\x120.backend.proto.offering.v1.ListOBServicesRequest\x1a1.backend.proto.offering.v1.ListOBServicesResponse\x12x\n" +
	"\x0fUpdateOBService\x121.backend.proto.offering.v1.UpdateOBServiceRequest\x1a2.backend.proto.offering.v1.UpdateOBServiceResponse\x12\x8a\x01\n" +
	"\x15ListAvailableServices\x127.backend.proto.offering.v1.ListAvailableServicesRequest\x1a8.backend.proto.offering.v1.ListAvailableServicesResponse\x12\x84\x01\n" +
	"\x13BatchUpdateServices\x125.backend.proto.offering.v1.BatchUpdateServicesRequest\x1a6.backend.proto.offering.v1.BatchUpdateServicesResponse\x12{\n" +
	"\x10BatchGetServices\x122.backend.proto.offering.v1.BatchGetServicesRequest\x1a3.backend.proto.offering.v1.BatchGetServicesResponse\x12\x99\x01\n" +
	"\x1aBatchGetCustomizedServices\x12<.backend.proto.offering.v1.BatchGetCustomizedServicesRequest\x1a=.backend.proto.offering.v1.BatchGetCustomizedServicesResponse\x12x\n" +
	"\x0fListPetOverride\x121.backend.proto.offering.v1.ListPetOverrideRequest\x1a2.backend.proto.offering.v1.ListPetOverrideResponse\x12~\n" +
	"\x11UpsertPetOverride\x123.backend.proto.offering.v1.UpsertPetOverrideRequest\x1a4.backend.proto.offering.v1.UpsertPetOverrideResponse\x12~\n" +
	"\x11UpdatePetOverride\x123.backend.proto.offering.v1.UpdatePetOverrideRequest\x1a4.backend.proto.offering.v1.UpdatePetOverrideResponse\x12~\n" +
	"\x11DeletePetOverride\x123.backend.proto.offering.v1.DeletePetOverrideRequest\x1a4.backend.proto.offering.v1.DeletePetOverrideResponse\x12\x8a\x01\n" +
	"\x15ListServiceCategories\x127.backend.proto.offering.v1.ListServiceCategoriesRequest\x1a8.backend.proto.offering.v1.ListServiceCategoriesResponse\x12\x8a\x01\n" +
	"\x15SaveServiceCategories\x127.backend.proto.offering.v1.SaveServiceCategoriesRequest\x1a8.backend.proto.offering.v1.SaveServiceCategoriesResponseBk\n" +
	"#com.moego.backend.proto.offering.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_service_service_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_service_service_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_service_service_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_service_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_service_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_service_proto_rawDesc), len(file_backend_proto_offering_v1_service_service_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_service_service_proto_rawDescData
}

var file_backend_proto_offering_v1_service_service_proto_msgTypes = make([]protoimpl.MessageInfo, 49)
var file_backend_proto_offering_v1_service_service_proto_goTypes = []any{
	(*DeleteServiceRequest)(nil),                                              // 0: backend.proto.offering.v1.DeleteServiceRequest
	(*DeleteServiceResponse)(nil),                                             // 1: backend.proto.offering.v1.DeleteServiceResponse
	(*UpdateServiceRequest)(nil),                                              // 2: backend.proto.offering.v1.UpdateServiceRequest
	(*UpdateServiceResponse)(nil),                                             // 3: backend.proto.offering.v1.UpdateServiceResponse
	(*CreateServiceRequest)(nil),                                              // 4: backend.proto.offering.v1.CreateServiceRequest
	(*CreateServiceResponse)(nil),                                             // 5: backend.proto.offering.v1.CreateServiceResponse
	(*GetServiceRequest)(nil),                                                 // 6: backend.proto.offering.v1.GetServiceRequest
	(*GetServiceResponse)(nil),                                                // 7: backend.proto.offering.v1.GetServiceResponse
	(*CategoryService)(nil),                                                   // 8: backend.proto.offering.v1.CategoryService
	(*ListOBServicesRequest)(nil),                                             // 9: backend.proto.offering.v1.ListOBServicesRequest
	(*ListOBServicesResponse)(nil),                                            // 10: backend.proto.offering.v1.ListOBServicesResponse
	(*ListServicesRequest)(nil),                                               // 11: backend.proto.offering.v1.ListServicesRequest
	(*ListServicesResponse)(nil),                                              // 12: backend.proto.offering.v1.ListServicesResponse
	(*BatchUpdateServicesRequest)(nil),                                        // 13: backend.proto.offering.v1.BatchUpdateServicesRequest
	(*ServiceUpdateDef)(nil),                                                  // 14: backend.proto.offering.v1.ServiceUpdateDef
	(*BatchUpdateServicesResponse)(nil),                                       // 15: backend.proto.offering.v1.BatchUpdateServicesResponse
	(*ListAvailableServicesRequest)(nil),                                      // 16: backend.proto.offering.v1.ListAvailableServicesRequest
	(*ListAvailableServicesResponse)(nil),                                     // 17: backend.proto.offering.v1.ListAvailableServicesResponse
	(*AvailableService)(nil),                                                  // 18: backend.proto.offering.v1.AvailableService
	(*AvailableServiceAttributes)(nil),                                        // 19: backend.proto.offering.v1.AvailableServiceAttributes
	(*UpdateOBServiceRequest)(nil),                                            // 20: backend.proto.offering.v1.UpdateOBServiceRequest
	(*UpdateOBServiceResponse)(nil),                                           // 21: backend.proto.offering.v1.UpdateOBServiceResponse
	(*BatchGetServicesRequest)(nil),                                           // 22: backend.proto.offering.v1.BatchGetServicesRequest
	(*BatchGetServicesResponse)(nil),                                          // 23: backend.proto.offering.v1.BatchGetServicesResponse
	(*ServiceCreateDef)(nil),                                                  // 24: backend.proto.offering.v1.ServiceCreateDef
	(*ListPetOverrideRequest)(nil),                                            // 25: backend.proto.offering.v1.ListPetOverrideRequest
	(*ListPetOverrideResponse)(nil),                                           // 26: backend.proto.offering.v1.ListPetOverrideResponse
	(*UpsertPetOverrideRequest)(nil),                                          // 27: backend.proto.offering.v1.UpsertPetOverrideRequest
	(*UpsertPetOverrideResponse)(nil),                                         // 28: backend.proto.offering.v1.UpsertPetOverrideResponse
	(*UpdatePetOverrideRequest)(nil),                                          // 29: backend.proto.offering.v1.UpdatePetOverrideRequest
	(*UpdatePetOverrideResponse)(nil),                                         // 30: backend.proto.offering.v1.UpdatePetOverrideResponse
	(*DeletePetOverrideRequest)(nil),                                          // 31: backend.proto.offering.v1.DeletePetOverrideRequest
	(*DeletePetOverrideResponse)(nil),                                         // 32: backend.proto.offering.v1.DeletePetOverrideResponse
	(*PetOverrideDef)(nil),                                                    // 33: backend.proto.offering.v1.PetOverrideDef
	(*ListServiceCategoriesRequest)(nil),                                      // 34: backend.proto.offering.v1.ListServiceCategoriesRequest
	(*ListServiceCategoriesResponse)(nil),                                     // 35: backend.proto.offering.v1.ListServiceCategoriesResponse
	(*SaveServiceCategoriesRequest)(nil),                                      // 36: backend.proto.offering.v1.SaveServiceCategoriesRequest
	(*SaveServiceCategoriesResponse)(nil),                                     // 37: backend.proto.offering.v1.SaveServiceCategoriesResponse
	(*BatchGetCustomizedServicesRequest)(nil),                                 // 38: backend.proto.offering.v1.BatchGetCustomizedServicesRequest
	(*BatchGetCustomizedServicesResponse)(nil),                                // 39: backend.proto.offering.v1.BatchGetCustomizedServicesResponse
	(*ListOBServicesRequest_Filter)(nil),                                      // 40: backend.proto.offering.v1.ListOBServicesRequest.Filter
	(*ListServicesRequest_Filter)(nil),                                        // 41: backend.proto.offering.v1.ListServicesRequest.Filter
	(*ListAvailableServicesRequest_AvailabilityContext)(nil),                  // 42: backend.proto.offering.v1.ListAvailableServicesRequest.AvailabilityContext
	(*ListAvailableServicesRequest_Filter)(nil),                               // 43: backend.proto.offering.v1.ListAvailableServicesRequest.Filter
	(*ListServiceCategoriesRequest_Filter)(nil),                               // 44: backend.proto.offering.v1.ListServiceCategoriesRequest.Filter
	(*SaveServiceCategoriesRequest_CategoryUpdateDef)(nil),                    // 45: backend.proto.offering.v1.SaveServiceCategoriesRequest.CategoryUpdateDef
	(*SaveServiceCategoriesRequest_CategoryCreateDef)(nil),                    // 46: backend.proto.offering.v1.SaveServiceCategoriesRequest.CategoryCreateDef
	(*BatchGetCustomizedServicesRequest_CustomizedServiceQueryCondition)(nil), // 47: backend.proto.offering.v1.BatchGetCustomizedServicesRequest.CustomizedServiceQueryCondition
	(*BatchGetCustomizedServicesResponse_ServiceWithCustomized)(nil),          // 48: backend.proto.offering.v1.BatchGetCustomizedServicesResponse.ServiceWithCustomized
	(*Service)(nil),                                                           // 49: backend.proto.offering.v1.Service
	(*PaginationRef)(nil),                                                     // 50: backend.proto.offering.v1.PaginationRef
	(*ServiceOBSetting)(nil),                                                  // 51: backend.proto.offering.v1.ServiceOBSetting
	(v1.OrganizationType)(0),                                                  // 52: backend.proto.organization.v1.OrganizationType
	(Service_Status)(0),                                                       // 53: backend.proto.offering.v1.Service.Status
	(*AvailableBusiness)(nil),                                                 // 54: backend.proto.offering.v1.AvailableBusiness
	(*ServiceAttributes)(nil),                                                 // 55: backend.proto.offering.v1.ServiceAttributes
	(*AdditionalService)(nil),                                                 // 56: backend.proto.offering.v1.AdditionalService
	(*AvailablePetTypeBreed)(nil),                                             // 57: backend.proto.offering.v1.AvailablePetTypeBreed
	(*AvailablePetSize)(nil),                                                  // 58: backend.proto.offering.v1.AvailablePetSize
	(*AvailableCoatType)(nil),                                                 // 59: backend.proto.offering.v1.AvailableCoatType
	(*AvailablePetCode)(nil),                                                  // 60: backend.proto.offering.v1.AvailablePetCode
	(*AvailablePetWeight)(nil),                                                // 61: backend.proto.offering.v1.AvailablePetWeight
	(*money.Money)(nil),                                                       // 62: google.type.Money
	(*BusinessStaffOverride)(nil),                                             // 63: backend.proto.offering.v1.BusinessStaffOverride
	(OfferingSource)(0),                                                       // 64: backend.proto.offering.v1.OfferingSource
	(*PetOverride)(nil),                                                       // 65: backend.proto.offering.v1.PetOverride
	(PriceUnit)(0),                                                            // 66: backend.proto.offering.v1.PriceUnit
	(ServiceOBSetting_ShowBasePriceMode)(0),                                   // 67: backend.proto.offering.v1.ServiceOBSetting.ShowBasePriceMode
	(ServiceOBSetting_ShowDuration)(0),                                        // 68: backend.proto.offering.v1.ServiceOBSetting.ShowDuration
	(*ServiceCategory)(nil),                                                   // 69: backend.proto.offering.v1.ServiceCategory
	(*latlng.LatLng)(nil),                                                     // 70: google.type.LatLng
	(*CustomizedService)(nil),                                                 // 71: backend.proto.offering.v1.CustomizedService
}
var file_backend_proto_offering_v1_service_service_proto_depIdxs = []int32{
	14, // 0: backend.proto.offering.v1.UpdateServiceRequest.service:type_name -> backend.proto.offering.v1.ServiceUpdateDef
	24, // 1: backend.proto.offering.v1.CreateServiceRequest.service:type_name -> backend.proto.offering.v1.ServiceCreateDef
	49, // 2: backend.proto.offering.v1.GetServiceResponse.service:type_name -> backend.proto.offering.v1.Service
	49, // 3: backend.proto.offering.v1.CategoryService.services:type_name -> backend.proto.offering.v1.Service
	40, // 4: backend.proto.offering.v1.ListOBServicesRequest.filter:type_name -> backend.proto.offering.v1.ListOBServicesRequest.Filter
	50, // 5: backend.proto.offering.v1.ListOBServicesRequest.pagination:type_name -> backend.proto.offering.v1.PaginationRef
	51, // 6: backend.proto.offering.v1.ListOBServicesResponse.service_ob_settings:type_name -> backend.proto.offering.v1.ServiceOBSetting
	50, // 7: backend.proto.offering.v1.ListOBServicesResponse.pagination:type_name -> backend.proto.offering.v1.PaginationRef
	52, // 8: backend.proto.offering.v1.ListServicesRequest.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	41, // 9: backend.proto.offering.v1.ListServicesRequest.filter:type_name -> backend.proto.offering.v1.ListServicesRequest.Filter
	50, // 10: backend.proto.offering.v1.ListServicesRequest.pagination:type_name -> backend.proto.offering.v1.PaginationRef
	49, // 11: backend.proto.offering.v1.ListServicesResponse.services:type_name -> backend.proto.offering.v1.Service
	50, // 12: backend.proto.offering.v1.ListServicesResponse.pagination:type_name -> backend.proto.offering.v1.PaginationRef
	52, // 13: backend.proto.offering.v1.BatchUpdateServicesRequest.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	14, // 14: backend.proto.offering.v1.BatchUpdateServicesRequest.update_services:type_name -> backend.proto.offering.v1.ServiceUpdateDef
	53, // 15: backend.proto.offering.v1.ServiceUpdateDef.status:type_name -> backend.proto.offering.v1.Service.Status
	54, // 16: backend.proto.offering.v1.ServiceUpdateDef.available_business:type_name -> backend.proto.offering.v1.AvailableBusiness
	55, // 17: backend.proto.offering.v1.ServiceUpdateDef.attributes:type_name -> backend.proto.offering.v1.ServiceAttributes
	56, // 18: backend.proto.offering.v1.ServiceUpdateDef.additional_service:type_name -> backend.proto.offering.v1.AdditionalService
	57, // 19: backend.proto.offering.v1.ServiceUpdateDef.available_type_breed:type_name -> backend.proto.offering.v1.AvailablePetTypeBreed
	58, // 20: backend.proto.offering.v1.ServiceUpdateDef.available_pet_size:type_name -> backend.proto.offering.v1.AvailablePetSize
	59, // 21: backend.proto.offering.v1.ServiceUpdateDef.available_coat_type:type_name -> backend.proto.offering.v1.AvailableCoatType
	60, // 22: backend.proto.offering.v1.ServiceUpdateDef.available_pet_code:type_name -> backend.proto.offering.v1.AvailablePetCode
	61, // 23: backend.proto.offering.v1.ServiceUpdateDef.available_pet_weight:type_name -> backend.proto.offering.v1.AvailablePetWeight
	62, // 24: backend.proto.offering.v1.ServiceUpdateDef.price:type_name -> google.type.Money
	63, // 25: backend.proto.offering.v1.ServiceUpdateDef.business_staff_overrides:type_name -> backend.proto.offering.v1.BusinessStaffOverride
	52, // 26: backend.proto.offering.v1.ListAvailableServicesRequest.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	42, // 27: backend.proto.offering.v1.ListAvailableServicesRequest.context:type_name -> backend.proto.offering.v1.ListAvailableServicesRequest.AvailabilityContext
	43, // 28: backend.proto.offering.v1.ListAvailableServicesRequest.filter:type_name -> backend.proto.offering.v1.ListAvailableServicesRequest.Filter
	50, // 29: backend.proto.offering.v1.ListAvailableServicesRequest.pagination:type_name -> backend.proto.offering.v1.PaginationRef
	18, // 30: backend.proto.offering.v1.ListAvailableServicesResponse.services:type_name -> backend.proto.offering.v1.AvailableService
	50, // 31: backend.proto.offering.v1.ListAvailableServicesResponse.pagination:type_name -> backend.proto.offering.v1.PaginationRef
	64, // 32: backend.proto.offering.v1.AvailableService.source:type_name -> backend.proto.offering.v1.OfferingSource
	53, // 33: backend.proto.offering.v1.AvailableService.status:type_name -> backend.proto.offering.v1.Service.Status
	62, // 34: backend.proto.offering.v1.AvailableService.price:type_name -> google.type.Money
	19, // 35: backend.proto.offering.v1.AvailableService.attributes:type_name -> backend.proto.offering.v1.AvailableServiceAttributes
	63, // 36: backend.proto.offering.v1.AvailableService.business_staff_overrides:type_name -> backend.proto.offering.v1.BusinessStaffOverride
	65, // 37: backend.proto.offering.v1.AvailableService.pet_overrides:type_name -> backend.proto.offering.v1.PetOverride
	66, // 38: backend.proto.offering.v1.AvailableServiceAttributes.price_unit:type_name -> backend.proto.offering.v1.PriceUnit
	67, // 39: backend.proto.offering.v1.UpdateOBServiceRequest.show_base_price:type_name -> backend.proto.offering.v1.ServiceOBSetting.ShowBasePriceMode
	68, // 40: backend.proto.offering.v1.UpdateOBServiceRequest.show_duration:type_name -> backend.proto.offering.v1.ServiceOBSetting.ShowDuration
	52, // 41: backend.proto.offering.v1.BatchGetServicesRequest.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	49, // 42: backend.proto.offering.v1.BatchGetServicesResponse.services:type_name -> backend.proto.offering.v1.Service
	52, // 43: backend.proto.offering.v1.ServiceCreateDef.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	64, // 44: backend.proto.offering.v1.ServiceCreateDef.source:type_name -> backend.proto.offering.v1.OfferingSource
	53, // 45: backend.proto.offering.v1.ServiceCreateDef.status:type_name -> backend.proto.offering.v1.Service.Status
	54, // 46: backend.proto.offering.v1.ServiceCreateDef.available_business:type_name -> backend.proto.offering.v1.AvailableBusiness
	56, // 47: backend.proto.offering.v1.ServiceCreateDef.additional_service:type_name -> backend.proto.offering.v1.AdditionalService
	57, // 48: backend.proto.offering.v1.ServiceCreateDef.available_type_breed:type_name -> backend.proto.offering.v1.AvailablePetTypeBreed
	58, // 49: backend.proto.offering.v1.ServiceCreateDef.available_pet_size:type_name -> backend.proto.offering.v1.AvailablePetSize
	59, // 50: backend.proto.offering.v1.ServiceCreateDef.available_coat_type:type_name -> backend.proto.offering.v1.AvailableCoatType
	60, // 51: backend.proto.offering.v1.ServiceCreateDef.available_pet_code:type_name -> backend.proto.offering.v1.AvailablePetCode
	55, // 52: backend.proto.offering.v1.ServiceCreateDef.attributes:type_name -> backend.proto.offering.v1.ServiceAttributes
	61, // 53: backend.proto.offering.v1.ServiceCreateDef.available_pet_weight:type_name -> backend.proto.offering.v1.AvailablePetWeight
	62, // 54: backend.proto.offering.v1.ServiceCreateDef.price:type_name -> google.type.Money
	63, // 55: backend.proto.offering.v1.ServiceCreateDef.business_staff_overrides:type_name -> backend.proto.offering.v1.BusinessStaffOverride
	50, // 56: backend.proto.offering.v1.ListPetOverrideRequest.pagination:type_name -> backend.proto.offering.v1.PaginationRef
	65, // 57: backend.proto.offering.v1.ListPetOverrideResponse.pet_overrides:type_name -> backend.proto.offering.v1.PetOverride
	50, // 58: backend.proto.offering.v1.ListPetOverrideResponse.pagination:type_name -> backend.proto.offering.v1.PaginationRef
	33, // 59: backend.proto.offering.v1.UpsertPetOverrideRequest.pet_override:type_name -> backend.proto.offering.v1.PetOverrideDef
	65, // 60: backend.proto.offering.v1.UpsertPetOverrideResponse.pet_override:type_name -> backend.proto.offering.v1.PetOverride
	33, // 61: backend.proto.offering.v1.UpdatePetOverrideRequest.pet_override:type_name -> backend.proto.offering.v1.PetOverrideDef
	65, // 62: backend.proto.offering.v1.UpdatePetOverrideResponse.pet_override:type_name -> backend.proto.offering.v1.PetOverride
	62, // 63: backend.proto.offering.v1.PetOverrideDef.price:type_name -> google.type.Money
	52, // 64: backend.proto.offering.v1.ListServiceCategoriesRequest.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	44, // 65: backend.proto.offering.v1.ListServiceCategoriesRequest.filter:type_name -> backend.proto.offering.v1.ListServiceCategoriesRequest.Filter
	69, // 66: backend.proto.offering.v1.ListServiceCategoriesResponse.categories:type_name -> backend.proto.offering.v1.ServiceCategory
	52, // 67: backend.proto.offering.v1.SaveServiceCategoriesRequest.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	46, // 68: backend.proto.offering.v1.SaveServiceCategoriesRequest.create_categories:type_name -> backend.proto.offering.v1.SaveServiceCategoriesRequest.CategoryCreateDef
	45, // 69: backend.proto.offering.v1.SaveServiceCategoriesRequest.update_categories:type_name -> backend.proto.offering.v1.SaveServiceCategoriesRequest.CategoryUpdateDef
	52, // 70: backend.proto.offering.v1.BatchGetCustomizedServicesRequest.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	47, // 71: backend.proto.offering.v1.BatchGetCustomizedServicesRequest.query_conditions:type_name -> backend.proto.offering.v1.BatchGetCustomizedServicesRequest.CustomizedServiceQueryCondition
	48, // 72: backend.proto.offering.v1.BatchGetCustomizedServicesResponse.customized_services:type_name -> backend.proto.offering.v1.BatchGetCustomizedServicesResponse.ServiceWithCustomized
	53, // 73: backend.proto.offering.v1.ListServicesRequest.Filter.statuses:type_name -> backend.proto.offering.v1.Service.Status
	53, // 74: backend.proto.offering.v1.ListAvailableServicesRequest.Filter.statuses:type_name -> backend.proto.offering.v1.Service.Status
	64, // 75: backend.proto.offering.v1.ListAvailableServicesRequest.Filter.sources:type_name -> backend.proto.offering.v1.OfferingSource
	70, // 76: backend.proto.offering.v1.BatchGetCustomizedServicesRequest.CustomizedServiceQueryCondition.coordinate:type_name -> google.type.LatLng
	71, // 77: backend.proto.offering.v1.BatchGetCustomizedServicesResponse.ServiceWithCustomized.customized_service:type_name -> backend.proto.offering.v1.CustomizedService
	4,  // 78: backend.proto.offering.v1.ServiceService.CreateService:input_type -> backend.proto.offering.v1.CreateServiceRequest
	6,  // 79: backend.proto.offering.v1.ServiceService.GetService:input_type -> backend.proto.offering.v1.GetServiceRequest
	2,  // 80: backend.proto.offering.v1.ServiceService.UpdateService:input_type -> backend.proto.offering.v1.UpdateServiceRequest
	0,  // 81: backend.proto.offering.v1.ServiceService.DeleteService:input_type -> backend.proto.offering.v1.DeleteServiceRequest
	11, // 82: backend.proto.offering.v1.ServiceService.ListServices:input_type -> backend.proto.offering.v1.ListServicesRequest
	9,  // 83: backend.proto.offering.v1.ServiceService.ListOBServices:input_type -> backend.proto.offering.v1.ListOBServicesRequest
	20, // 84: backend.proto.offering.v1.ServiceService.UpdateOBService:input_type -> backend.proto.offering.v1.UpdateOBServiceRequest
	16, // 85: backend.proto.offering.v1.ServiceService.ListAvailableServices:input_type -> backend.proto.offering.v1.ListAvailableServicesRequest
	13, // 86: backend.proto.offering.v1.ServiceService.BatchUpdateServices:input_type -> backend.proto.offering.v1.BatchUpdateServicesRequest
	22, // 87: backend.proto.offering.v1.ServiceService.BatchGetServices:input_type -> backend.proto.offering.v1.BatchGetServicesRequest
	38, // 88: backend.proto.offering.v1.ServiceService.BatchGetCustomizedServices:input_type -> backend.proto.offering.v1.BatchGetCustomizedServicesRequest
	25, // 89: backend.proto.offering.v1.ServiceService.ListPetOverride:input_type -> backend.proto.offering.v1.ListPetOverrideRequest
	27, // 90: backend.proto.offering.v1.ServiceService.UpsertPetOverride:input_type -> backend.proto.offering.v1.UpsertPetOverrideRequest
	29, // 91: backend.proto.offering.v1.ServiceService.UpdatePetOverride:input_type -> backend.proto.offering.v1.UpdatePetOverrideRequest
	31, // 92: backend.proto.offering.v1.ServiceService.DeletePetOverride:input_type -> backend.proto.offering.v1.DeletePetOverrideRequest
	34, // 93: backend.proto.offering.v1.ServiceService.ListServiceCategories:input_type -> backend.proto.offering.v1.ListServiceCategoriesRequest
	36, // 94: backend.proto.offering.v1.ServiceService.SaveServiceCategories:input_type -> backend.proto.offering.v1.SaveServiceCategoriesRequest
	5,  // 95: backend.proto.offering.v1.ServiceService.CreateService:output_type -> backend.proto.offering.v1.CreateServiceResponse
	7,  // 96: backend.proto.offering.v1.ServiceService.GetService:output_type -> backend.proto.offering.v1.GetServiceResponse
	3,  // 97: backend.proto.offering.v1.ServiceService.UpdateService:output_type -> backend.proto.offering.v1.UpdateServiceResponse
	1,  // 98: backend.proto.offering.v1.ServiceService.DeleteService:output_type -> backend.proto.offering.v1.DeleteServiceResponse
	12, // 99: backend.proto.offering.v1.ServiceService.ListServices:output_type -> backend.proto.offering.v1.ListServicesResponse
	10, // 100: backend.proto.offering.v1.ServiceService.ListOBServices:output_type -> backend.proto.offering.v1.ListOBServicesResponse
	21, // 101: backend.proto.offering.v1.ServiceService.UpdateOBService:output_type -> backend.proto.offering.v1.UpdateOBServiceResponse
	17, // 102: backend.proto.offering.v1.ServiceService.ListAvailableServices:output_type -> backend.proto.offering.v1.ListAvailableServicesResponse
	15, // 103: backend.proto.offering.v1.ServiceService.BatchUpdateServices:output_type -> backend.proto.offering.v1.BatchUpdateServicesResponse
	23, // 104: backend.proto.offering.v1.ServiceService.BatchGetServices:output_type -> backend.proto.offering.v1.BatchGetServicesResponse
	39, // 105: backend.proto.offering.v1.ServiceService.BatchGetCustomizedServices:output_type -> backend.proto.offering.v1.BatchGetCustomizedServicesResponse
	26, // 106: backend.proto.offering.v1.ServiceService.ListPetOverride:output_type -> backend.proto.offering.v1.ListPetOverrideResponse
	28, // 107: backend.proto.offering.v1.ServiceService.UpsertPetOverride:output_type -> backend.proto.offering.v1.UpsertPetOverrideResponse
	30, // 108: backend.proto.offering.v1.ServiceService.UpdatePetOverride:output_type -> backend.proto.offering.v1.UpdatePetOverrideResponse
	32, // 109: backend.proto.offering.v1.ServiceService.DeletePetOverride:output_type -> backend.proto.offering.v1.DeletePetOverrideResponse
	35, // 110: backend.proto.offering.v1.ServiceService.ListServiceCategories:output_type -> backend.proto.offering.v1.ListServiceCategoriesResponse
	37, // 111: backend.proto.offering.v1.ServiceService.SaveServiceCategories:output_type -> backend.proto.offering.v1.SaveServiceCategoriesResponse
	95, // [95:112] is the sub-list for method output_type
	78, // [78:95] is the sub-list for method input_type
	78, // [78:78] is the sub-list for extension type_name
	78, // [78:78] is the sub-list for extension extendee
	0,  // [0:78] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_service_service_proto_init() }
func file_backend_proto_offering_v1_service_service_proto_init() {
	if File_backend_proto_offering_v1_service_service_proto != nil {
		return
	}
	file_backend_proto_offering_v1_common_proto_init()
	file_backend_proto_offering_v1_service_proto_init()
	file_backend_proto_offering_v1_service_ob_setting_proto_init()
	file_backend_proto_offering_v1_service_category_proto_init()
	file_backend_proto_offering_v1_service_service_proto_msgTypes[10].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[12].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[14].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[16].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[17].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[18].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[19].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[20].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[24].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[33].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[42].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[43].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[47].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_service_proto_rawDesc), len(file_backend_proto_offering_v1_service_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   49,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_offering_v1_service_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_service_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_offering_v1_service_service_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_service_service_proto = out.File
	file_backend_proto_offering_v1_service_service_proto_goTypes = nil
	file_backend_proto_offering_v1_service_service_proto_depIdxs = nil
}
