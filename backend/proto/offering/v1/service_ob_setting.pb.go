// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.10
// 	protoc        (unknown)
// source: backend/proto/offering/v1/service_ob_setting.proto

package offeringpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Display price mode for online booking
// (-- api-linter: core::0126::unspecified=disabled
// aip.dev/not-precedent: 兼容老数据，使用 0 作为 Do not show price 的枚举 --)
type ServiceOBSetting_ShowBasePriceMode int32

const (
	// Do not show price
	ServiceOBSetting_SHOW_BASE_PRICE_MODE_DO_NOT_SHOW ServiceOBSetting_ShowBasePriceMode = 0
	// Show fixed price
	ServiceOBSetting_SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE ServiceOBSetting_ShowBasePriceMode = 1
	// Show "starting at" price
	ServiceOBSetting_SHOW_BASE_PRICE_MODE_SHOW_STARTING_AT ServiceOBSetting_ShowBasePriceMode = 2
	// Show "varies" price
	ServiceOBSetting_SHOW_BASE_PRICE_MODE_SHOW_VARIES ServiceOBSetting_ShowBasePriceMode = 3
)

// Enum value maps for ServiceOBSetting_ShowBasePriceMode.
var (
	ServiceOBSetting_ShowBasePriceMode_name = map[int32]string{
		0: "SHOW_BASE_PRICE_MODE_DO_NOT_SHOW",
		1: "SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE",
		2: "SHOW_BASE_PRICE_MODE_SHOW_STARTING_AT",
		3: "SHOW_BASE_PRICE_MODE_SHOW_VARIES",
	}
	ServiceOBSetting_ShowBasePriceMode_value = map[string]int32{
		"SHOW_BASE_PRICE_MODE_DO_NOT_SHOW":      0,
		"SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE": 1,
		"SHOW_BASE_PRICE_MODE_SHOW_STARTING_AT": 2,
		"SHOW_BASE_PRICE_MODE_SHOW_VARIES":      3,
	}
)

func (x ServiceOBSetting_ShowBasePriceMode) Enum() *ServiceOBSetting_ShowBasePriceMode {
	p := new(ServiceOBSetting_ShowBasePriceMode)
	*p = x
	return p
}

func (x ServiceOBSetting_ShowBasePriceMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceOBSetting_ShowBasePriceMode) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_service_ob_setting_proto_enumTypes[0].Descriptor()
}

func (ServiceOBSetting_ShowBasePriceMode) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_service_ob_setting_proto_enumTypes[0]
}

func (x ServiceOBSetting_ShowBasePriceMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceOBSetting_ShowBasePriceMode.Descriptor instead.
func (ServiceOBSetting_ShowBasePriceMode) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_ob_setting_proto_rawDescGZIP(), []int{0, 0}
}

// service in ob show duration
// (-- api-linter: core::0126::unspecified=disabled
// aip.dev/not-precedent: 兼容老数据，使用 0 作为 do not show duration 的枚举 --)
type ServiceOBSetting_ShowDuration int32

const (
	// do not show duration
	ServiceOBSetting_SHOW_DURATION_NO ServiceOBSetting_ShowDuration = 0
	// show duration
	ServiceOBSetting_SHOW_DURATION ServiceOBSetting_ShowDuration = 1
)

// Enum value maps for ServiceOBSetting_ShowDuration.
var (
	ServiceOBSetting_ShowDuration_name = map[int32]string{
		0: "SHOW_DURATION_NO",
		1: "SHOW_DURATION",
	}
	ServiceOBSetting_ShowDuration_value = map[string]int32{
		"SHOW_DURATION_NO": 0,
		"SHOW_DURATION":    1,
	}
)

func (x ServiceOBSetting_ShowDuration) Enum() *ServiceOBSetting_ShowDuration {
	p := new(ServiceOBSetting_ShowDuration)
	*p = x
	return p
}

func (x ServiceOBSetting_ShowDuration) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceOBSetting_ShowDuration) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_service_ob_setting_proto_enumTypes[1].Descriptor()
}

func (ServiceOBSetting_ShowDuration) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_service_ob_setting_proto_enumTypes[1]
}

func (x ServiceOBSetting_ShowDuration) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceOBSetting_ShowDuration.Descriptor instead.
func (ServiceOBSetting_ShowDuration) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_ob_setting_proto_rawDescGZIP(), []int{0, 1}
}

// Defines the structure for a service online booking setting, which controls how a service appears and behaves in online booking.
type ServiceOBSetting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Reference to the service template
	ServiceId int64 `protobuf:"varint,4,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// Whether the service is available for online booking
	IsAvailable bool `protobuf:"varint,5,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
	// Display price mode: 0 - Do not show, 1 - Show fixed price, 2 - Show "starting at", 3 - Show "varies"
	ShowBasePrice ServiceOBSetting_ShowBasePriceMode `protobuf:"varint,6,opt,name=show_base_price,json=showBasePrice,proto3,enum=backend.proto.offering.v1.ServiceOBSetting_ShowBasePriceMode" json:"show_base_price,omitempty"`
	// Whether all staff are available for this service when booking online
	IsAllStaff bool `protobuf:"varint,7,opt,name=is_all_staff,json=isAllStaff,proto3" json:"is_all_staff,omitempty"`
	// Whether all staff are available for this service when booking online
	StaffIds []int64 `protobuf:"varint,8,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
	// Display duration mode: 0 - Do not show duration, 1 - Show duration
	ShowDuration ServiceOBSetting_ShowDuration `protobuf:"varint,9,opt,name=show_duration,json=showDuration,proto3,enum=backend.proto.offering.v1.ServiceOBSetting_ShowDuration" json:"show_duration,omitempty"`
	// Whether this service can be booked together with other care types in one appointment
	AllowGroupBooking bool `protobuf:"varint,10,opt,name=allow_group_booking,json=allowGroupBooking,proto3" json:"allow_group_booking,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ServiceOBSetting) Reset() {
	*x = ServiceOBSetting{}
	mi := &file_backend_proto_offering_v1_service_ob_setting_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceOBSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceOBSetting) ProtoMessage() {}

func (x *ServiceOBSetting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_ob_setting_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceOBSetting.ProtoReflect.Descriptor instead.
func (*ServiceOBSetting) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_ob_setting_proto_rawDescGZIP(), []int{0}
}

func (x *ServiceOBSetting) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ServiceOBSetting) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *ServiceOBSetting) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

func (x *ServiceOBSetting) GetShowBasePrice() ServiceOBSetting_ShowBasePriceMode {
	if x != nil {
		return x.ShowBasePrice
	}
	return ServiceOBSetting_SHOW_BASE_PRICE_MODE_DO_NOT_SHOW
}

func (x *ServiceOBSetting) GetIsAllStaff() bool {
	if x != nil {
		return x.IsAllStaff
	}
	return false
}

func (x *ServiceOBSetting) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

func (x *ServiceOBSetting) GetShowDuration() ServiceOBSetting_ShowDuration {
	if x != nil {
		return x.ShowDuration
	}
	return ServiceOBSetting_SHOW_DURATION_NO
}

func (x *ServiceOBSetting) GetAllowGroupBooking() bool {
	if x != nil {
		return x.AllowGroupBooking
	}
	return false
}

var File_backend_proto_offering_v1_service_ob_setting_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_service_ob_setting_proto_rawDesc = "" +
	"\n" +
	"2backend/proto/offering/v1/service_ob_setting.proto\x12\x19backend.proto.offering.v1\"\x9b\x05\n" +
	"\x10ServiceOBSetting\x12\x1f\n" +
	"\vbusiness_id\x18\x03 \x01(\x03R\n" +
	"businessId\x12\x1d\n" +
	"\n" +
	"service_id\x18\x04 \x01(\x03R\tserviceId\x12!\n" +
	"\fis_available\x18\x05 \x01(\bR\visAvailable\x12e\n" +
	"\x0fshow_base_price\x18\x06 \x01(\x0e2=.backend.proto.offering.v1.ServiceOBSetting.ShowBasePriceModeR\rshowBasePrice\x12 \n" +
	"\fis_all_staff\x18\a \x01(\bR\n" +
	"isAllStaff\x12\x1b\n" +
	"\tstaff_ids\x18\b \x03(\x03R\bstaffIds\x12]\n" +
	"\rshow_duration\x18\t \x01(\x0e28.backend.proto.offering.v1.ServiceOBSetting.ShowDurationR\fshowDuration\x12.\n" +
	"\x13allow_group_booking\x18\n" +
	" \x01(\bR\x11allowGroupBooking\"\xb5\x01\n" +
	"\x11ShowBasePriceMode\x12$\n" +
	" SHOW_BASE_PRICE_MODE_DO_NOT_SHOW\x10\x00\x12)\n" +
	"%SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE\x10\x01\x12)\n" +
	"%SHOW_BASE_PRICE_MODE_SHOW_STARTING_AT\x10\x02\x12$\n" +
	" SHOW_BASE_PRICE_MODE_SHOW_VARIES\x10\x03\"7\n" +
	"\fShowDuration\x12\x14\n" +
	"\x10SHOW_DURATION_NO\x10\x00\x12\x11\n" +
	"\rSHOW_DURATION\x10\x01B\x87\x01\n" +
	"#com.moego.backend.proto.offering.v1B\x1aServiceOBSettingOuterClassP\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_service_ob_setting_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_service_ob_setting_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_service_ob_setting_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_service_ob_setting_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_service_ob_setting_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_ob_setting_proto_rawDesc), len(file_backend_proto_offering_v1_service_ob_setting_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_service_ob_setting_proto_rawDescData
}

var file_backend_proto_offering_v1_service_ob_setting_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_backend_proto_offering_v1_service_ob_setting_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_backend_proto_offering_v1_service_ob_setting_proto_goTypes = []any{
	(ServiceOBSetting_ShowBasePriceMode)(0), // 0: backend.proto.offering.v1.ServiceOBSetting.ShowBasePriceMode
	(ServiceOBSetting_ShowDuration)(0),      // 1: backend.proto.offering.v1.ServiceOBSetting.ShowDuration
	(*ServiceOBSetting)(nil),                // 2: backend.proto.offering.v1.ServiceOBSetting
}
var file_backend_proto_offering_v1_service_ob_setting_proto_depIdxs = []int32{
	0, // 0: backend.proto.offering.v1.ServiceOBSetting.show_base_price:type_name -> backend.proto.offering.v1.ServiceOBSetting.ShowBasePriceMode
	1, // 1: backend.proto.offering.v1.ServiceOBSetting.show_duration:type_name -> backend.proto.offering.v1.ServiceOBSetting.ShowDuration
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_service_ob_setting_proto_init() }
func file_backend_proto_offering_v1_service_ob_setting_proto_init() {
	if File_backend_proto_offering_v1_service_ob_setting_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_ob_setting_proto_rawDesc), len(file_backend_proto_offering_v1_service_ob_setting_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_offering_v1_service_ob_setting_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_service_ob_setting_proto_depIdxs,
		EnumInfos:         file_backend_proto_offering_v1_service_ob_setting_proto_enumTypes,
		MessageInfos:      file_backend_proto_offering_v1_service_ob_setting_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_service_ob_setting_proto = out.File
	file_backend_proto_offering_v1_service_ob_setting_proto_goTypes = nil
	file_backend_proto_offering_v1_service_ob_setting_proto_depIdxs = nil
}
