syntax = "proto3";

package backend.proto.offering.v1;

option go_package = "github.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpb";
option java_multiple_files = true;
option java_outer_classname = "ServiceOBSettingOuterClass";
option java_package = "com.moego.backend.proto.offering.v1";

// Defines the structure for a service online booking setting, which controls how a service appears and behaves in online booking.
message ServiceOBSetting {
  // business id
  int64 business_id = 3;

  // Reference to the service template
  int64 service_id = 4;

  // Whether the service is available for online booking
  bool is_available = 5;

  // Display price mode: 0 - Do not show, 1 - Show fixed price, 2 - Show "starting at", 3 - Show "varies"
  ShowBasePriceMode show_base_price = 6;

  // Whether all staff are available for this service when booking online
  bool is_all_staff = 7;

  // Whether all staff are available for this service when booking online
  repeated int64 staff_ids = 8;

  // Display price mode for online booking
  // (-- api-linter: core::0126::unspecified=disabled
  // aip.dev/not-precedent: 兼容老数据，使用 0 作为 Do not show price 的枚举 --)
  enum ShowBasePriceMode {
    // Do not show price
    SHOW_BASE_PRICE_MODE_DO_NOT_SHOW = 0;
    // Show fixed price
    SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE = 1;
    // Show "starting at" price
    SHOW_BASE_PRICE_MODE_SHOW_STARTING_AT = 2;
    // Show "varies" price
    SHOW_BASE_PRICE_MODE_SHOW_VARIES = 3;
  }

  // service in ob show duration
  // (-- api-linter: core::0126::unspecified=disabled
  // aip.dev/not-precedent: 兼容老数据，使用 0 作为 do not show duration 的枚举 --)
  enum ShowDuration {
    // do not show duration
    SHOW_DURATION_NO = 0;
    // show duration
    SHOW_DURATION = 1;
  }
}